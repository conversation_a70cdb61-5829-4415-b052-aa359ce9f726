spring.application.name=course-study-web-server
server.port = 8086
logging.level.root=INFO

dubbo.application.name=course-study-web-server
dubbo.application.version=1
#dubbo.registry.address=zookeeper://**************:10501
dubbo.registry.address=zookeeper://127.0.0.1:2181
dubbo.registry.username=zk_user
dubbo.registry.password=dreamtech
dubbo.registry.client=curator

spring.rabbitmq.host=*************
spring.rabbitmq.port=30419
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest
spring.rabbitmq.virtual-host=/
spring.rabbitmq.default-exchange=amq.direct
spring.rabbitmq.course.web.gensee.exchange=amq.topic
spring.rabbitmq.listener.simple.prefetch = 1
spring.rabbitmq.listener.simple.concurrency = 1
spring.rabbitmq.listener.simple.max-concurrency = 1

#graphite.server=**************
graphite.server=**************
graphite.port=30004
# fastdfs
spring.fastdfs.connect-timeout = 30
spring.fastdfs.network-timeout = 60
spring.fastdfs.charset = utf-8
spring.fastdfs.tracker-servers = *************:10401
spring.fastdfs.tracker-http-port = 10402
spring.fastdfs.anti-steal-token = false
spring.fastdfs.secret-key = 123456

# redis
spring.redis.cluster = false
spring.redis.timeout = 10000
spring.redis.cluster.nodes = *************:30016
spring.redis.password = TA6sMiuSRqtTrHB7Amdg
spring.redis.connection-timeout = 2000
spring.redis.max-attempts = 3
#spring.redis.cluster.nodes = *************:30006

app.secretKey.sm4=e83d7a1c9b046f25d2c5e789a0b4f67d


# jedis pool
spring.jedis.max-total=8
spring.jedis.max-idle=8
spring.jedis.block-when-exhausted=true
spring.jedis.max-wait-millis=-1

server.context-path=/api/v1/course-study

# upload config
spring.http.multipart.max-file-size=100Mb
spring.http.multipart.max-request-size=300Mb
spring.http.multipart.file-size-threshold=0

# getOnlineUsers live online number
onlineUsers_gensee_web_cast_URL=https://zxy9.zhixueyun.com/api/v1/cloud-center/gensee/getOnlineUsers
aes.key=d8cg8gVakEq9Agup

#yunshixun
yunshixun_create_gensee_web_cast_URL=http://api.vilin.zhumu.me/v20/meeting/createScheduledMeeting
yunshixun_delete_gensee_web_cast_URL=http://api.vilin.zhumu.me/v20/meeting/delete
yunshixun_update_gensee_web_cast_URL=http://api.vilin.zhumu.me/v20/meeting/update
yunshixun_token_gensee_web_cast_URL=http://meeting.125339.com.cn/mixapi/token
yunshixun_create_seminar_gensee_web_cast_URL=http://api.vilin.zhumu.me/v20/webinar/create
yunshixun_update_seminar_gensee_web_cast_URL=http://api.vilin.zhumu.me/v20/webinar/update
yunshixun_delete_seminar_gensee_web_cast_URL=http://api.vilin.zhumu.me/v20/webinar/cancel


secret.key=a19de0eb80013fb468fd2ee0cc672211
pccw.record.secret.key=486f239d7f0255e790d9ec8d9f4e0c6a

#\u4E13\u5BB6\u5DE5\u4F5C\u5BA4\u9ED8\u8BA4\u5185\u90E8\u7EC4\u7EC7
studio.default.organization=10000001
hot.visit.rank.cache.expire.time=600

#??????????????????id  ????
studio.policy.subject.ids=zt_56910010,zt_5860001,eb113057-598b-4a3a-aa8b-536d589128f1
#\u7F51\u5927\u8BFE\u7A0B\u7B14\u8BB0|\u8BFE\u4EF6\u7B14\u8BB0|\u731C\u4F60\u60F3\u95EE\u76F8\u5173Api
ai.mentor.synchronize.synchronizeNoteApi=http://***********:8082/v1/recommend/wangda/findSubjectIntroduction

# \u515A\u5EFA\u4E91\u5C4F\u4E13\u9898id - \u515A\u652F\u90E8\u4E66\u8BB0\u5B66\u4E60\u8BA4\u8BC1\u4E13\u533A2.0
djyp.subject.id = 50f2fc42-ac69-4643-bf17-5962a129347e
# \u515A\u5EFA\u4E91\u5C4F\u8003\u8BD5id
djyp.exam.id = bbfd30e9-2fe4-42b2-a67c-4715191194a4
# \u515A\u5EFA\u4E91\u5C4F\u8001\u515A\u652F\u90E8\u4E66\u8BB0id
djyp.thematic.id = 18d35a16-0d53-11ea-ae76-0050568d3015

course.model.url: http://exam.cmiivip.com
course.model.dataset-id:c2e4f95b-0518-4fb9-bea9-d4d8c1dceefb
course.model.app-api-key: Bearer app-9KIvY4ycYBL7PtIhdhKYk9fi
course.model.dataset-api-key: Bearer dataset-wyJ5dGtiGiMsqk7nrg0Etve2
course.model.interface.chat-messages = /assistant/v1/chat-messages
course.model.interface.chat-stop = /assistant/v1/chat-messages/%s/stop
course.model.interface.chat-list = /assistant/v1/conversations?user=%s&limit=%s&sort_by=%s
course.model.interface.history-chat-list = /assistant/v1/messages?user=%s&conversation_id=%s&limit=%s
course.model.interface.create-file = /assistant/v1/datasets/%s/document/create_by_file
course.ai.file.path = course-study-web-server/src/main/resources/file/



