package com.zxy.product.course.web.controller;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.RequestContext;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.CourseInfoService;
import com.zxy.product.course.api.DigitalIntelligenceMentorService;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.DigitalIntelligenceMentor;
import com.zxy.product.course.web.config.PropertiesConfig;
import com.zxy.product.course.web.util.DateUtil;
import com.zxy.product.system.entity.Member;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;
import java.util.Map;

/**
 * 数智导师-专题
 */
@Controller
@RequestMapping("/digital-mentor")
public class DigitalMentorController {
    private static final Logger logger = LoggerFactory.getLogger(DigitalMentorController.class);

    private CourseInfoService courseInfoService;
    private DigitalIntelligenceMentorService digitalIntelligenceMentorService;
    private static final Long ONE_DAY = 24 * 3600000L;
    private MessageSender messageSender;

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setCourseInfoService(CourseInfoService courseInfoService) {
        this.courseInfoService = courseInfoService;
    }

    @Autowired
    public void setDigitalIntelligenceMentorService(DigitalIntelligenceMentorService digitalIntelligenceMentorService) {
        this.digitalIntelligenceMentorService = digitalIntelligenceMentorService;
    }

    /**
     * 手动获取前一天发布的专题
     */
    @JSON("*")
    @RequestMapping(value = "/published-the-day-before-subject", method = RequestMethod.GET)
    public Map<String, String> publishedTheDayBeforeSubject() {
        // 获取前一天开始时间
        long endTime = DateUtil.getTimesnightByLong(System.currentTimeMillis() - ONE_DAY);
        long startTime = DateUtil.getTimesmorningByLong(System.currentTimeMillis() - ONE_DAY);
        logger.error(" 数智导师-专题， startTime={}, endTime={}", startTime, endTime);
        List<String> courseIds = courseInfoService.publishedTheDayBeforeSubject(startTime, endTime);
        if (CollectionUtils.isEmpty(courseIds)){
            return ImmutableMap.of("success", "false");
        }
        String ids = String.join(",", courseIds);
        logger.error("数据:{}", ids);
        messageSender.send(MessageTypeContent.DIGITAL_MENTOR_TOPIC, MessageHeaderContent.IDS, ids);
        logger.error("publishedTheDayBeforeSubject已启动: 数智导师-专题， startTime={}", System.currentTimeMillis());
        return ImmutableMap.of("success", "true");
    }

    /**
     * 根据用户ID查询最新的会话记录
     * @param requestContext 请求上下文
     * @param subject 当前用户
     * @return 会话记录列表
     */
    @JSON("id,memberId,userQuery,botResponse,order,createTime")
    @RequestMapping(value = "/conversations", method = RequestMethod.GET)
    @Param(name = "memberId", type = String.class, required = true)
    @Permitted
    public List<DigitalIntelligenceMentor> getLatestConversations(RequestContext requestContext, Subject<Member> subject) {
        String memberId = requestContext.getString("memberId");
        Integer limit = PropertiesConfig.getDigitalIntelligenceMentorConversationLimit();

        logger.info("查询用户{}的最新{}次会话记录", memberId, limit);

        List<DigitalIntelligenceMentor> conversations = digitalIntelligenceMentorService.findLatestConversationsByMemberId(memberId, limit);

        logger.info("查询到{}条会话记录", conversations.size());

        return conversations;
    }
}
