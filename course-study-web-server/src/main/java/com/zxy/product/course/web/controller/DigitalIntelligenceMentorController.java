package com.zxy.product.course.web.controller;

import com.alibaba.fastjson.JSON;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.DigitalIntelligenceMentorService;
import com.zxy.product.course.entity.DigitalIntelligenceMentor;
import com.zxy.product.course.web.dto.ModelRequestDto;
import com.zxy.product.course.web.dto.ModelResponseDto;
import com.zxy.product.system.entity.Member;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 数智导师对话记录控制器
 */
@Controller
@RequestMapping("/digital-intelligence-mentor")
public class DigitalIntelligenceMentorController {
    
    private static final Logger logger = LoggerFactory.getLogger(DigitalIntelligenceMentorController.class);

    private DigitalIntelligenceMentorService digitalIntelligenceMentorService;

    @Value("${digital.intelligence.mentor.conversation.limit:30}")
    private Integer conversationLimit;

    @Value("${digital.intelligence.mentor.model.url}")
    private String modelUrl;

    private RestTemplate restTemplate;

    @Autowired
    public void setDigitalIntelligenceMentorService(DigitalIntelligenceMentorService digitalIntelligenceMentorService) {
        this.digitalIntelligenceMentorService = digitalIntelligenceMentorService;
    }

    @Autowired
    public void setRestTemplate(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    /**
     * 根据用户ID查询最新的大模型对话记录
     * @param requestContext 请求上下文
     * @param subject 当前用户
     * @return 对话记录列表，按order字段降序排列
     */
    @JSON("id,memberId,userQuery,botResponse,order,createTime")
    @RequestMapping(value = "/latest-records", method = RequestMethod.GET)
    @Param(name = "memberId", type = String.class, required = true)
    @Permitted
    public List<DigitalIntelligenceMentor> getLatestRecords(RequestContext requestContext, Subject<Member> subject) {
        String memberId = requestContext.getString("memberId");

        logger.info("查询用户{}的最新{}条大模型对话记录", memberId, conversationLimit);

        List<DigitalIntelligenceMentor> records = digitalIntelligenceMentorService.findLatestConversationsByMemberId(memberId, conversationLimit);

        logger.info("查询到{}条对话记录", records.size());

        return records;
    }

    /**
     * 提交问题给大模型并获取回答
     * @param requestContext 请求上下文
     * @param subject 当前用户
     * @return 创建的对话记录
     */
    @JSON("id,memberId,userQuery,botResponse,order,createTime")
    @RequestMapping(value = "/ask-model", method = RequestMethod.POST)
    @Param(name = "memberId", type = String.class, required = true)
    @Param(name = "userQuery", type = String.class, required = true)
    @Permitted
    public DigitalIntelligenceMentor askModel(RequestContext requestContext, Subject<Member> subject) {
        String memberId = requestContext.getString("memberId");
        String userQuery = requestContext.getString("userQuery");

        logger.info("用户{}提交问题给大模型: {}", memberId, userQuery);

        // 1. 先写入数据库，生成记录ID作为msgId
        DigitalIntelligenceMentor record = new DigitalIntelligenceMentor();
        record.forInsert(); // 生成ID和创建时间
        record.setMemberId(memberId);
        record.setUserQuery(userQuery);
        record.setOrder(1); // 可以根据业务需要设置order值

        // 插入数据库
        digitalIntelligenceMentorService.insert(record);
        String msgId = record.getId();

        logger.info("已创建对话记录，ID: {}", msgId);

        try {
            // 2. 调用大模型接口
            ModelRequestDto modelRequest = new ModelRequestDto(msgId, userQuery);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<ModelRequestDto> requestEntity = new HttpEntity<>(modelRequest, headers);

            logger.info("调用大模型接口: {}, 请求参数: {}", modelUrl, modelRequest);

            ResponseEntity<String> response = restTemplate.postForEntity(modelUrl, requestEntity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                logger.info("大模型响应: {}", responseBody);

                // 3. 解析响应
                ModelResponseDto modelResponse = JSON.parseObject(responseBody, ModelResponseDto.class);

                if ("0".equals(modelResponse.getCode()) && msgId.equals(modelResponse.getMsgId())) {
                    // 4. 更新数据库中的bot_response字段
                    digitalIntelligenceMentorService.updateBotResponse(msgId, modelResponse.getAnswer());
                    record.setBotResponse(modelResponse.getAnswer());

                    logger.info("已更新对话记录的大模型响应，ID: {}", msgId);
                } else {
                    logger.error("大模型响应异常，code: {}, msgId: {}, 期望msgId: {}",
                        modelResponse.getCode(), modelResponse.getMsgId(), msgId);
                }
            } else {
                logger.error("调用大模型接口失败，状态码: {}", response.getStatusCode());
            }

        } catch (Exception e) {
            logger.error("调用大模型接口异常，msgId: {}", msgId, e);
        }

        return record;
    }
}
