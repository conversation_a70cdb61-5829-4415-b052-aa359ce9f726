package com.zxy.product.course.web.controller;

import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.DigitalIntelligenceMentorService;
import com.zxy.product.course.entity.DigitalIntelligenceMentor;
import com.zxy.product.course.web.config.PropertiesConfig;
import com.zxy.product.system.entity.Member;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 数智导师会话记录控制器
 */
@Controller
@RequestMapping("/digital-intelligence-mentor")
public class DigitalIntelligenceMentorController {
    
    private static final Logger logger = LoggerFactory.getLogger(DigitalIntelligenceMentorController.class);

    private DigitalIntelligenceMentorService digitalIntelligenceMentorService;

    @Autowired
    public void setDigitalIntelligenceMentorService(DigitalIntelligenceMentorService digitalIntelligenceMentorService) {
        this.digitalIntelligenceMentorService = digitalIntelligenceMentorService;
    }

    /**
     * 根据用户ID查询最新的会话记录
     * @param requestContext 请求上下文
     * @param subject 当前用户
     * @return 会话记录列表
     */
    @JSON("id,memberId,userQuery,botResponse,order,createTime")
    @RequestMapping(value = "/conversations", method = RequestMethod.GET)
    @Param(name = "memberId", type = String.class, required = true)
    @Permitted
    public List<DigitalIntelligenceMentor> getLatestConversations(RequestContext requestContext, Subject<Member> subject) {
        String memberId = requestContext.getString("memberId");
        Integer limit = PropertiesConfig.getDigitalIntelligenceMentorConversationLimit();
        
        logger.info("查询用户{}的最新{}次会话记录", memberId, limit);
        
        List<DigitalIntelligenceMentor> conversations = digitalIntelligenceMentorService.findLatestConversationsByMemberId(memberId, limit);
        
        logger.info("查询到{}条会话记录", conversations.size());
        
        return conversations;
    }

    /**
     * 根据ID获取单个会话记录
     * @param requestContext 请求上下文
     * @param subject 当前用户
     * @return 会话记录
     */
    @JSON("id,memberId,userQuery,botResponse,order,createTime")
    @RequestMapping(value = "/conversation", method = RequestMethod.GET)
    @Param(name = "id", type = String.class, required = true)
    @Permitted
    public DigitalIntelligenceMentor getConversation(RequestContext requestContext, Subject<Member> subject) {
        String id = requestContext.getString("id");
        
        logger.info("查询会话记录，ID: {}", id);
        
        DigitalIntelligenceMentor conversation = digitalIntelligenceMentorService.get(id);
        
        if (conversation != null) {
            logger.info("查询到会话记录，用户ID: {}", conversation.getMemberId());
        } else {
            logger.warn("未找到会话记录，ID: {}", id);
        }
        
        return conversation;
    }
}
