/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.course.jooq;

import com.zxy.product.course.jooq.tables.*;
import com.zxy.product.course.jooq.tables.records.ExamRecord;
import com.zxy.product.course.jooq.tables.records.*;
import org.jooq.Identity;
import org.jooq.UniqueKey;
import org.jooq.impl.AbstractKeys;

import javax.annotation.Generated;


/**
 * A class modelling foreign key relationships between tables of the <code>course-study</code>
 * schema
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Keys {

    // -------------------------------------------------------------------------
    // IDENTITY definitions
    // -------------------------------------------------------------------------

    public static final Identity<DbaAnalyzeTableIndexRecord, Long> IDENTITY_DBA_ANALYZE_TABLE_INDEX = Identities0.IDENTITY_DBA_ANALYZE_TABLE_INDEX;
    public static final Identity<CourseOnlineLogRecord, Long> IDENTITY_COURSE_ONLINE_LOG = Identities0.IDENTITY_COURSE_ONLINE_LOG;

    // -------------------------------------------------------------------------
    // UNIQUE and PRIMARY KEY definitions
    // -------------------------------------------------------------------------
    public static final UniqueKey<MiguConfigRecord> KEY_T_MIGU_CONFIG_PRIMARY = UniqueKeys0.KEY_T_MIGU_CONFIG_PRIMARY;
    public static final UniqueKey<ActivityBannerRecord> KEY_T_ACTIVITY_BANNER_PRIMARY = UniqueKeys0.KEY_T_ACTIVITY_BANNER_PRIMARY;
    public static final UniqueKey<ActivityChbnRecord> KEY_T_ACTIVITY_CHBN_PRIMARY = UniqueKeys0.KEY_T_ACTIVITY_CHBN_PRIMARY;
    public static final UniqueKey<AnnualBill_2019Record> KEY_T_ANNUAL_BILL_2019_PRIMARY = UniqueKeys0.KEY_T_ANNUAL_BILL_2019_PRIMARY;
    public static final UniqueKey<AnnualBill_2021Record> KEY_T_ANNUAL_BILL_2021_PRIMARY = UniqueKeys0.KEY_T_ANNUAL_BILL_2021_PRIMARY;
    public static final UniqueKey<AnnualBillAskbarcommentRecord> KEY_T_ANNUAL_BILL_ASKBARCOMMENT_PRIMARY = UniqueKeys0.KEY_T_ANNUAL_BILL_ASKBARCOMMENT_PRIMARY;
    public static final UniqueKey<AnnualBillCoursecommentRecord> KEY_T_ANNUAL_BILL_COURSECOMMENT_PRIMARY = UniqueKeys0.KEY_T_ANNUAL_BILL_COURSECOMMENT_PRIMARY;
    public static final UniqueKey<AnnualBillExamRecord> KEY_T_ANNUAL_BILL_EXAM_PRIMARY = UniqueKeys0.KEY_T_ANNUAL_BILL_EXAM_PRIMARY;
    public static final UniqueKey<AnnualBillStudydayRecord> KEY_T_ANNUAL_BILL_STUDYDAY_PRIMARY = UniqueKeys0.KEY_T_ANNUAL_BILL_STUDYDAY_PRIMARY;
    public static final UniqueKey<AudienceItemRecord> KEY_T_AUDIENCE_ITEM_PRIMARY = UniqueKeys0.KEY_T_AUDIENCE_ITEM_PRIMARY;
    public static final UniqueKey<AudienceMemberRecord> KEY_T_AUDIENCE_MEMBER_PRIMARY = UniqueKeys0.KEY_T_AUDIENCE_MEMBER_PRIMARY;
    public static final UniqueKey<AudienceObjectRecord> KEY_T_AUDIENCE_OBJECT_PRIMARY = UniqueKeys0.KEY_T_AUDIENCE_OBJECT_PRIMARY;
    public static final UniqueKey<BillConfigRecord> KEY_T_BILL_CONFIG_PRIMARY = UniqueKeys0.KEY_T_BILL_CONFIG_PRIMARY;
    public static final UniqueKey<BillConfigLostRecord> KEY_T_BILL_CONFIG_LOST_PRIMARY = UniqueKeys0.KEY_T_BILL_CONFIG_LOST_PRIMARY;
    public static final UniqueKey<BusinessCertificateRecord> KEY_T_BUSINESS_CERTIFICATE_PRIMARY = UniqueKeys0.KEY_T_BUSINESS_CERTIFICATE_PRIMARY;
    public static final UniqueKey<BusinessCertificateRecord> KEY_T_BUSINESS_CERTIFICATE_UNIQUE_T_BUSINESS_CERTIFICATE_BUSINESSID = UniqueKeys0.KEY_T_BUSINESS_CERTIFICATE_UNIQUE_T_BUSINESS_CERTIFICATE_BUSINESSID;
    public static final UniqueKey<BusinessTopicRecord> KEY_T_BUSINESS_TOPIC_PRIMARY = UniqueKeys0.KEY_T_BUSINESS_TOPIC_PRIMARY;
    public static final UniqueKey<CertificateRecordRecord> KEY_T_CERTIFICATE_RECORD_PRIMARY = UniqueKeys0.KEY_T_CERTIFICATE_RECORD_PRIMARY;
    public static final UniqueKey<CertificateRecordRecord> KEY_T_CERTIFICATE_RECORD_UNIQUE_CERTIFICATE_RECORD_MEMBERID_BUSINESS_ID = UniqueKeys0.KEY_T_CERTIFICATE_RECORD_UNIQUE_CERTIFICATE_RECORD_MEMBERID_BUSINESS_ID;
    public static final UniqueKey<CertificateRecordChbnRecord> KEY_T_CERTIFICATE_RECORD_CHBN_PRIMARY = UniqueKeys0.KEY_T_CERTIFICATE_RECORD_CHBN_PRIMARY;
    public static final UniqueKey<CertificateRecordChbnRecord> KEY_T_CERTIFICATE_RECORD_CHBN_UNIQUE_CERTIFICATE_RECORD_MEMBERID_BUSINESS_ID = UniqueKeys0.KEY_T_CERTIFICATE_RECORD_CHBN_UNIQUE_CERTIFICATE_RECORD_MEMBERID_BUSINESS_ID;
    public static final UniqueKey<CompeteCourseAttachmentRecord> KEY_T_COMPETE_COURSE_ATTACHMENT_PRIMARY = UniqueKeys0.KEY_T_COMPETE_COURSE_ATTACHMENT_PRIMARY;
    public static final UniqueKey<CompeteCourseChapterSectionRecord> KEY_T_COMPETE_COURSE_CHAPTER_SECTION_PRIMARY = UniqueKeys0.KEY_T_COMPETE_COURSE_CHAPTER_SECTION_PRIMARY;
    public static final UniqueKey<CompeteCourseInfoRecord> KEY_T_COMPETE_COURSE_INFO_PRIMARY = UniqueKeys0.KEY_T_COMPETE_COURSE_INFO_PRIMARY;
    public static final UniqueKey<CompeteCourseVoteRecord> KEY_T_COMPETE_COURSE_VOTE_PRIMARY = UniqueKeys0.KEY_T_COMPETE_COURSE_VOTE_PRIMARY;
    public static final UniqueKey<CompeteCourseVoteRecord> KEY_T_COMPETE_COURSE_VOTE_VOTE_COURSE = UniqueKeys0.KEY_T_COMPETE_COURSE_VOTE_VOTE_COURSE;
    public static final UniqueKey<CompeteLecturerCompanyRecord> KEY_T_COMPETE_LECTURER_COMPANY_PRIMARY = UniqueKeys0.KEY_T_COMPETE_LECTURER_COMPANY_PRIMARY;
    public static final UniqueKey<CourseAttachmentRecord> KEY_T_COURSE_ATTACHMENT_PRIMARY = UniqueKeys0.KEY_T_COURSE_ATTACHMENT_PRIMARY;
    public static final UniqueKey<CourseCategoryRecord> KEY_T_COURSE_CATEGORY_PRIMARY = UniqueKeys0.KEY_T_COURSE_CATEGORY_PRIMARY;
    public static final UniqueKey<CourseCertificateRecordRecord> KEY_T_COURSE_CERTIFICATE_RECORD_PRIMARY = UniqueKeys0.KEY_T_COURSE_CERTIFICATE_RECORD_PRIMARY;
    public static final UniqueKey<CourseCertificateRecordRecord> KEY_T_COURSE_CERTIFICATE_RECORD_IDX_COURSE_CERTIFICATE_RECORD_UNIQUEKEY_MEMBERID = UniqueKeys0.KEY_T_COURSE_CERTIFICATE_RECORD_IDX_COURSE_CERTIFICATE_RECORD_UNIQUEKEY_MEMBERID;
    public static final UniqueKey<CourseChapterRecord> KEY_T_COURSE_CHAPTER_PRIMARY = UniqueKeys0.KEY_T_COURSE_CHAPTER_PRIMARY;
    public static final UniqueKey<CourseChapterQuestionnaireRecord> KEY_T_COURSE_CHAPTER_QUESTIONNAIRE_PRIMARY = UniqueKeys0.KEY_T_COURSE_CHAPTER_QUESTIONNAIRE_PRIMARY;
    public static final UniqueKey<CourseChapterSectionRecord> KEY_T_COURSE_CHAPTER_SECTION_PRIMARY = UniqueKeys0.KEY_T_COURSE_CHAPTER_SECTION_PRIMARY;
    public static final UniqueKey<CourseCurrencyRecord> KEY_T_COURSE_CURRENCY_PRIMARY = UniqueKeys0.KEY_T_COURSE_CURRENCY_PRIMARY;
    public static final UniqueKey<CourseExceptionRecord> KEY_T_COURSE_EXCEPTION_PRIMARY = UniqueKeys0.KEY_T_COURSE_EXCEPTION_PRIMARY;
    public static final UniqueKey<CourseInfoRecord> KEY_T_COURSE_INFO_PRIMARY = UniqueKeys0.KEY_T_COURSE_INFO_PRIMARY;
    public static final UniqueKey<CourseInfoCategoryRecord> KEY_T_COURSE_INFO_CATEGORY_PRIMARY = UniqueKeys0.KEY_T_COURSE_INFO_CATEGORY_PRIMARY;
    public static final UniqueKey<CourseInformRecord> KEY_T_COURSE_INFORM_PRIMARY = UniqueKeys0.KEY_T_COURSE_INFORM_PRIMARY;
    public static final UniqueKey<CourseNoteRecord> KEY_T_COURSE_NOTE_PRIMARY = UniqueKeys0.KEY_T_COURSE_NOTE_PRIMARY;
    public static final UniqueKey<CoursePhotoRecord> KEY_T_COURSE_PHOTO_PRIMARY = UniqueKeys0.KEY_T_COURSE_PHOTO_PRIMARY;
    public static final UniqueKey<CourseQuestionnaireRecordRecord> KEY_T_COURSE_QUESTIONNAIRE_RECORD_PRIMARY = UniqueKeys0.KEY_T_COURSE_QUESTIONNAIRE_RECORD_PRIMARY;
    public static final UniqueKey<CourseRecommendRecord> KEY_T_COURSE_RECOMMEND_PRIMARY = UniqueKeys0.KEY_T_COURSE_RECOMMEND_PRIMARY;
    public static final UniqueKey<CourseRecordRecord> KEY_T_COURSE_RECORD_PRIMARY = UniqueKeys0.KEY_T_COURSE_RECORD_PRIMARY;
    public static final UniqueKey<CourseRegisterRecord> KEY_T_COURSE_REGISTER_PRIMARY = UniqueKeys0.KEY_T_COURSE_REGISTER_PRIMARY;
    public static final UniqueKey<CourseScoreRecord> KEY_T_COURSE_SCORE_PRIMARY = UniqueKeys0.KEY_T_COURSE_SCORE_PRIMARY;
    public static final UniqueKey<CourseSectionProgressAttachmentRecord> KEY_T_COURSE_SECTION_PROGRESS_ATTACHMENT_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_PROGRESS_ATTACHMENT_PRIMARY;
    public static final UniqueKey<CourseSectionScormRecord> KEY_T_COURSE_SECTION_SCORM_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_SCORM_PRIMARY;
    public static final UniqueKey<CourseSectionScormProgressRecord> KEY_T_COURSE_SECTION_SCORM_PROGRESS_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_SCORM_PROGRESS_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogRecord> KEY_T_COURSE_SECTION_STUDY_LOG_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogAhRecord> KEY_T_COURSE_SECTION_STUDY_LOG_AH_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_AH_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogAhDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_AH_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_AH_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogBjRecord> KEY_T_COURSE_SECTION_STUDY_LOG_BJ_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_BJ_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogBjDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_BJ_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_BJ_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogCmRecord> KEY_T_COURSE_SECTION_STUDY_LOG_CM_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_CM_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogCmDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_CM_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_CM_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogCqRecord> KEY_T_COURSE_SECTION_STUDY_LOG_CQ_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_CQ_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogCqDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_CQ_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_CQ_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogEbRecord> KEY_T_COURSE_SECTION_STUDY_LOG_EB_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_EB_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogEbDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_EB_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_EB_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogFjRecord> KEY_T_COURSE_SECTION_STUDY_LOG_FJ_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_FJ_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogFjDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_FJ_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_FJ_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogGdRecord> KEY_T_COURSE_SECTION_STUDY_LOG_GD_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_GD_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogGdDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_GD_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_GD_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogGsRecord> KEY_T_COURSE_SECTION_STUDY_LOG_GS_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_GS_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogGsDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_GS_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_GS_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogGxRecord> KEY_T_COURSE_SECTION_STUDY_LOG_GX_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_GX_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogGxDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_GX_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_GX_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogGzRecord> KEY_T_COURSE_SECTION_STUDY_LOG_GZ_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_GZ_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogGzDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_GZ_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_GZ_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogHbRecord> KEY_T_COURSE_SECTION_STUDY_LOG_HB_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_HB_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogHbDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_HB_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_HB_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogHlRecord> KEY_T_COURSE_SECTION_STUDY_LOG_HL_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_HL_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogHlDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_HL_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_HL_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogHnRecord> KEY_T_COURSE_SECTION_STUDY_LOG_HN_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_HN_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogHnDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_HN_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_HN_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogJlRecord> KEY_T_COURSE_SECTION_STUDY_LOG_JL_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_JL_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogJlDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_JL_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_JL_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogJsRecord> KEY_T_COURSE_SECTION_STUDY_LOG_JS_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_JS_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogJsDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_JS_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_JS_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogJxRecord> KEY_T_COURSE_SECTION_STUDY_LOG_JX_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_JX_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogJxDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_JX_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_JX_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogLnRecord> KEY_T_COURSE_SECTION_STUDY_LOG_LN_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_LN_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogLnDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_LN_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_LN_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogNmRecord> KEY_T_COURSE_SECTION_STUDY_LOG_NM_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_NM_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogNmDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_NM_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_NM_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogNxRecord> KEY_T_COURSE_SECTION_STUDY_LOG_NX_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_NX_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogNxDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_NX_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_NX_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogOtherRecord> KEY_T_COURSE_SECTION_STUDY_LOG_OTHER_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_OTHER_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogOtherDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_OTHER_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_OTHER_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogQhRecord> KEY_T_COURSE_SECTION_STUDY_LOG_QH_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_QH_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogQhDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_QH_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_QH_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogQoRecord> KEY_T_COURSE_SECTION_STUDY_LOG_QO_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_QO_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogQoDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_QO_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_QO_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogScRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SC_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_SC_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogScDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SC_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_SC_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogSdRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SD_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_SD_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogSdDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SD_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_SD_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogShRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SH_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_SH_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogShDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SH_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_SH_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogSnRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SN_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_SN_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogSnDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SN_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_SN_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogSxRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SX_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_SX_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogSxDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SX_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_SX_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogTjRecord> KEY_T_COURSE_SECTION_STUDY_LOG_TJ_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_TJ_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogTjDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_TJ_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_TJ_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogXjRecord> KEY_T_COURSE_SECTION_STUDY_LOG_XJ_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_XJ_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogXjDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_XJ_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_XJ_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogXnRecord> KEY_T_COURSE_SECTION_STUDY_LOG_XN_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_XN_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogXnDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_XN_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_XN_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogXzRecord> KEY_T_COURSE_SECTION_STUDY_LOG_XZ_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_XZ_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogXzDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_XZ_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_XZ_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogYnRecord> KEY_T_COURSE_SECTION_STUDY_LOG_YN_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_YN_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogYnDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_YN_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_YN_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogZgttRecord> KEY_T_COURSE_SECTION_STUDY_LOG_ZGTT_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_ZGTT_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogZgttDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_ZGTT_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_ZGTT_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogZjRecord> KEY_T_COURSE_SECTION_STUDY_LOG_ZJ_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_ZJ_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogZjDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_ZJ_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_ZJ_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogZxRecord> KEY_T_COURSE_SECTION_STUDY_LOG_ZX_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_ZX_PRIMARY;
    public static final UniqueKey<CourseSectionStudyLogZxDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_ZX_DAY_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_LOG_ZX_DAY_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressChbncRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_CHBNC_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_CHBNC_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressChbncRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_CHBNC_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_CHBNC_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressChbnsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_CHBNS_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_CHBNS_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressChbnsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_CHBNS_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_CHBNS_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressFfclcRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressFfclcRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_UNIQUE_T_CSSP_FFCLC_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_UNIQUE_T_CSSP_FFCLC_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressFfclsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressFfclsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_UNIQUE_T_CSSP_FFCLS_SECTION_MEMBER = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_UNIQUE_T_CSSP_FFCLS_SECTION_MEMBER;
    public static final UniqueKey<CourseSectionStudyProgressFfcls_2022Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressFfcls_2022Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022_UNIQUE_T_CSSP_FFCLS_2022_SECTION_MEMBER = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022_UNIQUE_T_CSSP_FFCLS_2022_SECTION_MEMBER;
    public static final UniqueKey<CourseSectionStudyProgressRtsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_RTS_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_RTS_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressRtsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_RTS_UNIQUE_T_CSSP_XDNS_SECTION_MEMBER = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_RTS_UNIQUE_T_CSSP_XDNS_SECTION_MEMBER;
    public static final UniqueKey<CourseSectionStudyProgressXdncRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XDNC_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_XDNC_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressXdncRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XDNC_UNIQUE_T_CSSP_XDNC_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_XDNC_UNIQUE_T_CSSP_XDNC_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressXdnsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XDNS_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_XDNS_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressXdnsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XDNS_UNIQUE_T_CSSP_XDNS_SECTION_MEMBER = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_XDNS_UNIQUE_T_CSSP_XDNS_SECTION_MEMBER;
    public static final UniqueKey<CourseSectionStudyProgressZhztsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZHZTS_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZHZTS_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressZhztsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZHZTS_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZHZTS_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSequenceRecord> KEY_T_COURSE_SEQUENCE_PRIMARY = UniqueKeys0.KEY_T_COURSE_SEQUENCE_PRIMARY;
    public static final UniqueKey<CourseShelvesRecord> KEY_T_COURSE_SHELVES_PRIMARY = UniqueKeys0.KEY_T_COURSE_SHELVES_PRIMARY;
    public static final UniqueKey<CourseStudyProcess_2017Record> KEY_T_COURSE_STUDY_PROCESS_2017_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROCESS_2017_PRIMARY;
    public static final UniqueKey<CourseStudyProgressRecord> KEY_T_COURSE_STUDY_PROGRESS_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_PRIMARY;
    public static final UniqueKey<CourseStudyProgressRecord> KEY_T_COURSE_STUDY_PROGRESS_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressAhRecord> KEY_T_COURSE_STUDY_PROGRESS_AH_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_AH_PRIMARY;
    public static final UniqueKey<CourseStudyProgressAhRecord> KEY_T_COURSE_STUDY_PROGRESS_AH_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_AH_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressBjRecord> KEY_T_COURSE_STUDY_PROGRESS_BJ_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_BJ_PRIMARY;
    public static final UniqueKey<CourseStudyProgressBjRecord> KEY_T_COURSE_STUDY_PROGRESS_BJ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_BJ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressCmRecord> KEY_T_COURSE_STUDY_PROGRESS_CM_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_CM_PRIMARY;
    public static final UniqueKey<CourseStudyProgressCmRecord> KEY_T_COURSE_STUDY_PROGRESS_CM_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_CM_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressCqRecord> KEY_T_COURSE_STUDY_PROGRESS_CQ_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_CQ_PRIMARY;
    public static final UniqueKey<CourseStudyProgressCqRecord> KEY_T_COURSE_STUDY_PROGRESS_CQ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_CQ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressEbRecord> KEY_T_COURSE_STUDY_PROGRESS_EB_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_EB_PRIMARY;
    public static final UniqueKey<CourseStudyProgressEbRecord> KEY_T_COURSE_STUDY_PROGRESS_EB_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_EB_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressFjRecord> KEY_T_COURSE_STUDY_PROGRESS_FJ_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_FJ_PRIMARY;
    public static final UniqueKey<CourseStudyProgressFjRecord> KEY_T_COURSE_STUDY_PROGRESS_FJ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_FJ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressGdRecord> KEY_T_COURSE_STUDY_PROGRESS_GD_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_GD_PRIMARY;
    public static final UniqueKey<CourseStudyProgressGdRecord> KEY_T_COURSE_STUDY_PROGRESS_GD_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_GD_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressGsRecord> KEY_T_COURSE_STUDY_PROGRESS_GS_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_GS_PRIMARY;
    public static final UniqueKey<CourseStudyProgressGsRecord> KEY_T_COURSE_STUDY_PROGRESS_GS_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_GS_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressGxRecord> KEY_T_COURSE_STUDY_PROGRESS_GX_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_GX_PRIMARY;
    public static final UniqueKey<CourseStudyProgressGxRecord> KEY_T_COURSE_STUDY_PROGRESS_GX_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_GX_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressGzRecord> KEY_T_COURSE_STUDY_PROGRESS_GZ_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_GZ_PRIMARY;
    public static final UniqueKey<CourseStudyProgressGzRecord> KEY_T_COURSE_STUDY_PROGRESS_GZ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_GZ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressHbRecord> KEY_T_COURSE_STUDY_PROGRESS_HB_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_HB_PRIMARY;
    public static final UniqueKey<CourseStudyProgressHbRecord> KEY_T_COURSE_STUDY_PROGRESS_HB_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_HB_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressHlRecord> KEY_T_COURSE_STUDY_PROGRESS_HL_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_HL_PRIMARY;
    public static final UniqueKey<CourseStudyProgressHlRecord> KEY_T_COURSE_STUDY_PROGRESS_HL_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_HL_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressHnRecord> KEY_T_COURSE_STUDY_PROGRESS_HN_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_HN_PRIMARY;
    public static final UniqueKey<CourseStudyProgressHnRecord> KEY_T_COURSE_STUDY_PROGRESS_HN_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_HN_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressJlRecord> KEY_T_COURSE_STUDY_PROGRESS_JL_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_JL_PRIMARY;
    public static final UniqueKey<CourseStudyProgressJlRecord> KEY_T_COURSE_STUDY_PROGRESS_JL_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_JL_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressJsRecord> KEY_T_COURSE_STUDY_PROGRESS_JS_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_JS_PRIMARY;
    public static final UniqueKey<CourseStudyProgressJsRecord> KEY_T_COURSE_STUDY_PROGRESS_JS_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_JS_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressJxRecord> KEY_T_COURSE_STUDY_PROGRESS_JX_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_JX_PRIMARY;
    public static final UniqueKey<CourseStudyProgressJxRecord> KEY_T_COURSE_STUDY_PROGRESS_JX_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_JX_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressLnRecord> KEY_T_COURSE_STUDY_PROGRESS_LN_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_LN_PRIMARY;
    public static final UniqueKey<CourseStudyProgressLnRecord> KEY_T_COURSE_STUDY_PROGRESS_LN_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_LN_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressNmRecord> KEY_T_COURSE_STUDY_PROGRESS_NM_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_NM_PRIMARY;
    public static final UniqueKey<CourseStudyProgressNmRecord> KEY_T_COURSE_STUDY_PROGRESS_NM_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_NM_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressNxRecord> KEY_T_COURSE_STUDY_PROGRESS_NX_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_NX_PRIMARY;
    public static final UniqueKey<CourseStudyProgressNxRecord> KEY_T_COURSE_STUDY_PROGRESS_NX_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_NX_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressOtherRecord> KEY_T_COURSE_STUDY_PROGRESS_OTHER_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_OTHER_PRIMARY;
    public static final UniqueKey<CourseStudyProgressOtherRecord> KEY_T_COURSE_STUDY_PROGRESS_OTHER_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_OTHER_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressQhRecord> KEY_T_COURSE_STUDY_PROGRESS_QH_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_QH_PRIMARY;
    public static final UniqueKey<CourseStudyProgressQhRecord> KEY_T_COURSE_STUDY_PROGRESS_QH_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_QH_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressQoRecord> KEY_T_COURSE_STUDY_PROGRESS_QO_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_QO_PRIMARY;
    public static final UniqueKey<CourseStudyProgressQoRecord> KEY_T_COURSE_STUDY_PROGRESS_QO_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_QO_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressScRecord> KEY_T_COURSE_STUDY_PROGRESS_SC_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_SC_PRIMARY;
    public static final UniqueKey<CourseStudyProgressScRecord> KEY_T_COURSE_STUDY_PROGRESS_SC_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_SC_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressSdRecord> KEY_T_COURSE_STUDY_PROGRESS_SD_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_SD_PRIMARY;
    public static final UniqueKey<CourseStudyProgressSdRecord> KEY_T_COURSE_STUDY_PROGRESS_SD_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_SD_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressShRecord> KEY_T_COURSE_STUDY_PROGRESS_SH_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_SH_PRIMARY;
    public static final UniqueKey<CourseStudyProgressShRecord> KEY_T_COURSE_STUDY_PROGRESS_SH_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_SH_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressSnRecord> KEY_T_COURSE_STUDY_PROGRESS_SN_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_SN_PRIMARY;
    public static final UniqueKey<CourseStudyProgressSnRecord> KEY_T_COURSE_STUDY_PROGRESS_SN_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_SN_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressSxRecord> KEY_T_COURSE_STUDY_PROGRESS_SX_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_SX_PRIMARY;
    public static final UniqueKey<CourseStudyProgressSxRecord> KEY_T_COURSE_STUDY_PROGRESS_SX_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_SX_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressTjRecord> KEY_T_COURSE_STUDY_PROGRESS_TJ_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_TJ_PRIMARY;
    public static final UniqueKey<CourseStudyProgressTjRecord> KEY_T_COURSE_STUDY_PROGRESS_TJ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_TJ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressXjRecord> KEY_T_COURSE_STUDY_PROGRESS_XJ_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_XJ_PRIMARY;
    public static final UniqueKey<CourseStudyProgressXjRecord> KEY_T_COURSE_STUDY_PROGRESS_XJ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_XJ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressXnRecord> KEY_T_COURSE_STUDY_PROGRESS_XN_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_XN_PRIMARY;
    public static final UniqueKey<CourseStudyProgressXnRecord> KEY_T_COURSE_STUDY_PROGRESS_XN_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_XN_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressXzRecord> KEY_T_COURSE_STUDY_PROGRESS_XZ_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_XZ_PRIMARY;
    public static final UniqueKey<CourseStudyProgressXzRecord> KEY_T_COURSE_STUDY_PROGRESS_XZ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_XZ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressYnRecord> KEY_T_COURSE_STUDY_PROGRESS_YN_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_YN_PRIMARY;
    public static final UniqueKey<CourseStudyProgressYnRecord> KEY_T_COURSE_STUDY_PROGRESS_YN_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_YN_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressZgttRecord> KEY_T_COURSE_STUDY_PROGRESS_ZGTT_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ZGTT_PRIMARY;
    public static final UniqueKey<CourseStudyProgressZgttRecord> KEY_T_COURSE_STUDY_PROGRESS_ZGTT_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ZGTT_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressZjRecord> KEY_T_COURSE_STUDY_PROGRESS_ZJ_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ZJ_PRIMARY;
    public static final UniqueKey<CourseStudyProgressZjRecord> KEY_T_COURSE_STUDY_PROGRESS_ZJ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ZJ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressZxRecord> KEY_T_COURSE_STUDY_PROGRESS_ZX_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ZX_PRIMARY;
    public static final UniqueKey<CourseStudyProgressZxRecord> KEY_T_COURSE_STUDY_PROGRESS_ZX_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ZX_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseTopicRecord> KEY_T_COURSE_TOPIC_PRIMARY = UniqueKeys0.KEY_T_COURSE_TOPIC_PRIMARY;
    public static final UniqueKey<CourseVersionRecord> KEY_T_COURSE_VERSION_PRIMARY = UniqueKeys0.KEY_T_COURSE_VERSION_PRIMARY;
    public static final UniqueKey<DbaAnalyzeTableIndexRecord> KEY_T_DBA_ANALYZE_TABLE_INDEX_PRIMARY = UniqueKeys0.KEY_T_DBA_ANALYZE_TABLE_INDEX_PRIMARY;
    public static final UniqueKey<DjClassifyRecord> KEY_T_DJ_CLASSIFY_PRIMARY = UniqueKeys0.KEY_T_DJ_CLASSIFY_PRIMARY;
    public static final UniqueKey<DjResourceRecord> KEY_T_DJ_RESOURCE_PRIMARY = UniqueKeys0.KEY_T_DJ_RESOURCE_PRIMARY;
    public static final UniqueKey<ExamRecord> KEY_T_EXAM_PRIMARY = UniqueKeys0.KEY_T_EXAM_PRIMARY;
    public static final UniqueKey<ExamRecordRecord> KEY_T_EXAM_RECORD_PRIMARY = UniqueKeys0.KEY_T_EXAM_RECORD_PRIMARY;
    public static final UniqueKey<GenseeBusinessRecord> KEY_T_GENSEE_BUSINESS_PRIMARY = UniqueKeys0.KEY_T_GENSEE_BUSINESS_PRIMARY;
    public static final UniqueKey<GenseeBusinessProgressRecord> KEY_T_GENSEE_BUSINESS_PROGRESS_PRIMARY = UniqueKeys0.KEY_T_GENSEE_BUSINESS_PROGRESS_PRIMARY;
    public static final UniqueKey<GenseeLecturerRecord> KEY_T_GENSEE_LECTURER_PRIMARY = UniqueKeys0.KEY_T_GENSEE_LECTURER_PRIMARY;
    public static final UniqueKey<GenseeSubscriptionRecord> KEY_T_GENSEE_SUBSCRIPTION_PRIMARY = UniqueKeys0.KEY_T_GENSEE_SUBSCRIPTION_PRIMARY;
    public static final UniqueKey<GenseeTopicRecord> KEY_T_GENSEE_TOPIC_PRIMARY = UniqueKeys0.KEY_T_GENSEE_TOPIC_PRIMARY;
    public static final UniqueKey<GenseeUserAccessRecord> KEY_T_GENSEE_USER_ACCESS_PRIMARY = UniqueKeys0.KEY_T_GENSEE_USER_ACCESS_PRIMARY;
    public static final UniqueKey<GenseeUserAccessRecord> KEY_T_GENSEE_USER_ACCESS_UNIQUE_T_GENSEE_USER_ACCESS_MEMBER_GENSEE = UniqueKeys0.KEY_T_GENSEE_USER_ACCESS_UNIQUE_T_GENSEE_USER_ACCESS_MEMBER_GENSEE;
    public static final UniqueKey<GenseeUserJoinHistoryRecord> KEY_T_GENSEE_USER_JOIN_HISTORY_PRIMARY = UniqueKeys0.KEY_T_GENSEE_USER_JOIN_HISTORY_PRIMARY;
    public static final UniqueKey<GenseeWebCastRecord> KEY_T_GENSEE_WEB_CAST_PRIMARY = UniqueKeys0.KEY_T_GENSEE_WEB_CAST_PRIMARY;
    public static final UniqueKey<GrantDetailRecord> KEY_T_GRANT_DETAIL_PRIMARY = UniqueKeys0.KEY_T_GRANT_DETAIL_PRIMARY;
    public static final UniqueKey<JobRecord> KEY_T_JOB_PRIMARY = UniqueKeys0.KEY_T_JOB_PRIMARY;
    public static final UniqueKey<KnowledgeCategoryRecord> KEY_T_KNOWLEDGE_CATEGORY_PRIMARY = UniqueKeys0.KEY_T_KNOWLEDGE_CATEGORY_PRIMARY;
    public static final UniqueKey<KnowledgeDownRecordRecord> KEY_T_KNOWLEDGE_DOWN_RECORD_PRIMARY = UniqueKeys0.KEY_T_KNOWLEDGE_DOWN_RECORD_PRIMARY;
    public static final UniqueKey<KnowledgeInfoRecord> KEY_T_KNOWLEDGE_INFO_PRIMARY = UniqueKeys0.KEY_T_KNOWLEDGE_INFO_PRIMARY;
    public static final UniqueKey<KnowledgeMonthListRecord> KEY_T_KNOWLEDGE_MONTH_LIST_PRIMARY = UniqueKeys0.KEY_T_KNOWLEDGE_MONTH_LIST_PRIMARY;
    public static final UniqueKey<KnowledgeTopicRecord> KEY_T_KNOWLEDGE_TOPIC_PRIMARY = UniqueKeys0.KEY_T_KNOWLEDGE_TOPIC_PRIMARY;
    public static final UniqueKey<KnowledgeUseRecordRecord> KEY_T_KNOWLEDGE_USE_RECORD_PRIMARY = UniqueKeys0.KEY_T_KNOWLEDGE_USE_RECORD_PRIMARY;
    public static final UniqueKey<KnowledgeViewRecordRecord> KEY_T_KNOWLEDGE_VIEW_RECORD_PRIMARY = UniqueKeys0.KEY_T_KNOWLEDGE_VIEW_RECORD_PRIMARY;
    public static final UniqueKey<MemberRecord> KEY_T_MEMBER_PRIMARY = UniqueKeys0.KEY_T_MEMBER_PRIMARY;
    public static final UniqueKey<MemberCourseMonthRecord> KEY_T_MEMBER_COURSE_MONTH_PRIMARY = UniqueKeys0.KEY_T_MEMBER_COURSE_MONTH_PRIMARY;
    public static final UniqueKey<MemberDetailRecord> KEY_T_MEMBER_DETAIL_PRIMARY = UniqueKeys0.KEY_T_MEMBER_DETAIL_PRIMARY;
    public static final UniqueKey<MemberKnowledgeMonthRecord> KEY_T_MEMBER_KNOWLEDGE_MONTH_PRIMARY = UniqueKeys0.KEY_T_MEMBER_KNOWLEDGE_MONTH_PRIMARY;
    public static final UniqueKey<MemberPartyRecord> KEY_T_MEMBER_PARTY_PRIMARY = UniqueKeys0.KEY_T_MEMBER_PARTY_PRIMARY;
    public static final UniqueKey<MemberStatisticsRecord> KEY_T_MEMBER_STATISTICS_PRIMARY = UniqueKeys0.KEY_T_MEMBER_STATISTICS_PRIMARY;
    public static final UniqueKey<MemberStatisticsArchivesRecord> KEY_T_MEMBER_STATISTICS_ARCHIVES_PRIMARY = UniqueKeys0.KEY_T_MEMBER_STATISTICS_ARCHIVES_PRIMARY;
    public static final UniqueKey<NoticeRecord> KEY_T_NOTICE_PRIMARY = UniqueKeys0.KEY_T_NOTICE_PRIMARY;
    public static final UniqueKey<OfflineClassRecord> KEY_T_OFFLINE_CLASS_PRIMARY = UniqueKeys0.KEY_T_OFFLINE_CLASS_PRIMARY;
    public static final UniqueKey<OfflineCourseQuestionnaireRecord> KEY_T_OFFLINE_COURSE_QUESTIONNAIRE_PRIMARY = UniqueKeys0.KEY_T_OFFLINE_COURSE_QUESTIONNAIRE_PRIMARY;
    public static final UniqueKey<OfflineCourseQuestionnaireChapterRecord> KEY_T_OFFLINE_COURSE_QUESTIONNAIRE_CHAPTER_PRIMARY = UniqueKeys0.KEY_T_OFFLINE_COURSE_QUESTIONNAIRE_CHAPTER_PRIMARY;
    public static final UniqueKey<OfflineQuestionnaireAnswerRecord> KEY_T_OFFLINE_QUESTIONNAIRE_ANSWER_PRIMARY = UniqueKeys0.KEY_T_OFFLINE_QUESTIONNAIRE_ANSWER_PRIMARY;
    public static final UniqueKey<OnlineQuestionnaireAnswerRecord> KEY_T_ONLINE_QUESTIONNAIRE_ANSWER_PRIMARY = UniqueKeys0.KEY_T_ONLINE_QUESTIONNAIRE_ANSWER_PRIMARY;
    public static final UniqueKey<OrganizationRecord> KEY_T_ORGANIZATION_PRIMARY = UniqueKeys0.KEY_T_ORGANIZATION_PRIMARY;
    public static final UniqueKey<OrganizationDetailRecord> KEY_T_ORGANIZATION_DETAIL_PRIMARY = UniqueKeys0.KEY_T_ORGANIZATION_DETAIL_PRIMARY;
    public static final UniqueKey<OrgTableCountRecord> KEY_T_ORG_TABLE_COUNT_PRIMARY = UniqueKeys0.KEY_T_ORG_TABLE_COUNT_PRIMARY;
    public static final UniqueKey<PartyActivityMemberDayRecord> KEY_T_PARTY_ACTIVITY_MEMBER_DAY_PRIMARY = UniqueKeys0.KEY_T_PARTY_ACTIVITY_MEMBER_DAY_PRIMARY;
    public static final UniqueKey<PartyActivityMemberMonthRecord> KEY_T_PARTY_ACTIVITY_MEMBER_MONTH_PRIMARY = UniqueKeys0.KEY_T_PARTY_ACTIVITY_MEMBER_MONTH_PRIMARY;
    public static final UniqueKey<PartyActivityOrgDayRecord> KEY_T_PARTY_ACTIVITY_ORG_DAY_PRIMARY = UniqueKeys0.KEY_T_PARTY_ACTIVITY_ORG_DAY_PRIMARY;
    public static final UniqueKey<PartyActivityOrgMonthRecord> KEY_T_PARTY_ACTIVITY_ORG_MONTH_PRIMARY = UniqueKeys0.KEY_T_PARTY_ACTIVITY_ORG_MONTH_PRIMARY;
    public static final UniqueKey<PartyActivityOrgYearRecord> KEY_T_PARTY_ACTIVITY_ORG_YEAR_PRIMARY = UniqueKeys0.KEY_T_PARTY_ACTIVITY_ORG_YEAR_PRIMARY;
    public static final UniqueKey<PartyBusinessConfigurationRecord> KEY_T_PARTY_BUSINESS_CONFIGURATION_PRIMARY = UniqueKeys0.KEY_T_PARTY_BUSINESS_CONFIGURATION_PRIMARY;
    public static final UniqueKey<PartyDataRecord> KEY_T_PARTY_DATA_PRIMARY = UniqueKeys0.KEY_T_PARTY_DATA_PRIMARY;
    public static final UniqueKey<PartyHotTopicRecord> KEY_T_PARTY_HOT_TOPIC_PRIMARY = UniqueKeys0.KEY_T_PARTY_HOT_TOPIC_PRIMARY;
    public static final UniqueKey<PartyHotTopicManageRecord> KEY_T_PARTY_HOT_TOPIC_MANAGE_PRIMARY = UniqueKeys0.KEY_T_PARTY_HOT_TOPIC_MANAGE_PRIMARY;
    public static final UniqueKey<PartyLeaderRecord> KEY_T_PARTY_LEADER_PRIMARY = UniqueKeys0.KEY_T_PARTY_LEADER_PRIMARY;
    public static final UniqueKey<PartyOrganizationRecord> KEY_T_PARTY_ORGANIZATION_PRIMARY = UniqueKeys0.KEY_T_PARTY_ORGANIZATION_PRIMARY;
    public static final UniqueKey<PartyOrganizationRelationshipsRecord> KEY_T_PARTY_ORGANIZATION_RELATIONSHIPS_PRIMARY = UniqueKeys0.KEY_T_PARTY_ORGANIZATION_RELATIONSHIPS_PRIMARY;
    public static final UniqueKey<PartyRecommendationRecord> KEY_T_PARTY_RECOMMENDATION_PRIMARY = UniqueKeys0.KEY_T_PARTY_RECOMMENDATION_PRIMARY;
    public static final UniqueKey<PartyRecommendResultRecord> KEY_T_PARTY_RECOMMEND_RESULT_PRIMARY = UniqueKeys0.KEY_T_PARTY_RECOMMEND_RESULT_PRIMARY;
    public static final UniqueKey<PartyRecommendSpareRecord> KEY_T_PARTY_RECOMMEND_SPARE_PRIMARY = UniqueKeys0.KEY_T_PARTY_RECOMMEND_SPARE_PRIMARY;
    public static final UniqueKey<PartyStudySummaryDayRecord> KEY_T_PARTY_STUDY_SUMMARY_DAY_PRIMARY = UniqueKeys0.KEY_T_PARTY_STUDY_SUMMARY_DAY_PRIMARY;
    public static final UniqueKey<PartyStudySummaryMonthRecord> KEY_T_PARTY_STUDY_SUMMARY_MONTH_PRIMARY = UniqueKeys0.KEY_T_PARTY_STUDY_SUMMARY_MONTH_PRIMARY;
    public static final UniqueKey<PartyTopicRecord> KEY_T_PARTY_TOPIC_PRIMARY = UniqueKeys0.KEY_T_PARTY_TOPIC_PRIMARY;
    public static final UniqueKey<PccwOrganizationConfigRecord> KEY_T_PCCW_ORGANIZATION_CONFIG_PRIMARY = UniqueKeys0.KEY_T_PCCW_ORGANIZATION_CONFIG_PRIMARY;
    public static final UniqueKey<PccwResultRecord> KEY_T_PCCW_RESULT_PRIMARY = UniqueKeys0.KEY_T_PCCW_RESULT_PRIMARY;
    public static final UniqueKey<PccwResultBusinessRecord> KEY_T_PCCW_RESULT_BUSINESS_PRIMARY = UniqueKeys0.KEY_T_PCCW_RESULT_BUSINESS_PRIMARY;
    public static final UniqueKey<PccwResultErrorHistoryRecord> KEY_T_PCCW_RESULT_ERROR_HISTORY_PRIMARY = UniqueKeys0.KEY_T_PCCW_RESULT_ERROR_HISTORY_PRIMARY;
    public static final UniqueKey<PersonYearBillRecord> KEY_T_PERSON_YEAR_BILL_PRIMARY = UniqueKeys0.KEY_T_PERSON_YEAR_BILL_PRIMARY;
    public static final UniqueKey<PositionRecord> KEY_T_POSITION_PRIMARY = UniqueKeys0.KEY_T_POSITION_PRIMARY;
    public static final UniqueKey<PositionOldRecord> KEY_T_POSITION_OLD_PRIMARY = UniqueKeys0.KEY_T_POSITION_OLD_PRIMARY;
    public static final UniqueKey<QuestionnaireMouldRecord> KEY_T_QUESTIONNAIRE_MOULD_PRIMARY = UniqueKeys0.KEY_T_QUESTIONNAIRE_MOULD_PRIMARY;
    public static final UniqueKey<QuestionnaireMouldQuestionRecord> KEY_T_QUESTIONNAIRE_MOULD_QUESTION_PRIMARY = UniqueKeys0.KEY_T_QUESTIONNAIRE_MOULD_QUESTION_PRIMARY;
    public static final UniqueKey<QuestionnaireQuestionRecord> KEY_T_QUESTIONNAIRE_QUESTION_PRIMARY = UniqueKeys0.KEY_T_QUESTIONNAIRE_QUESTION_PRIMARY;
    public static final UniqueKey<RemodelingEntryStudyLogRecord> KEY_T_REMODELING_ENTRY_STUDY_LOG_PRIMARY = UniqueKeys0.KEY_T_REMODELING_ENTRY_STUDY_LOG_PRIMARY;
    public static final UniqueKey<RemodelingEntryStudyProgressRecord> KEY_T_REMODELING_ENTRY_STUDY_PROGRESS_PRIMARY = UniqueKeys0.KEY_T_REMODELING_ENTRY_STUDY_PROGRESS_PRIMARY;
    public static final UniqueKey<RemodelingEntryStudyProgressRecord> KEY_T_REMODELING_ENTRY_STUDY_PROGRESS_UNIQUE_T_REMODELING_ENTRY_STUDY_PROGRESS_MEMBERIDANDSUBJECTID = UniqueKeys0.KEY_T_REMODELING_ENTRY_STUDY_PROGRESS_UNIQUE_T_REMODELING_ENTRY_STUDY_PROGRESS_MEMBERIDANDSUBJECTID;
    public static final UniqueKey<RemodelingExternalCourseBusinessRecord> KEY_T_REMODELING_EXTERNAL_COURSE_BUSINESS_PRIMARY = UniqueKeys0.KEY_T_REMODELING_EXTERNAL_COURSE_BUSINESS_PRIMARY;
    public static final UniqueKey<RemodelingExternalCourseBusinessRecord> KEY_T_REMODELING_EXTERNAL_COURSE_BUSINESS_UNIQUE_EXTERNAL_COURSE_BUSINESS_EXTERNALCOURSEID_APPID = UniqueKeys0.KEY_T_REMODELING_EXTERNAL_COURSE_BUSINESS_UNIQUE_EXTERNAL_COURSE_BUSINESS_EXTERNALCOURSEID_APPID;
    public static final UniqueKey<RemodelingExternalCourseStudyDetailRecord> KEY_T_REMODELING_EXTERNAL_COURSE_STUDY_DETAIL_PRIMARY = UniqueKeys0.KEY_T_REMODELING_EXTERNAL_COURSE_STUDY_DETAIL_PRIMARY;
    public static final UniqueKey<RemodelingExternalExamDetailRecord> KEY_T_REMODELING_EXTERNAL_EXAM_DETAIL_PRIMARY = UniqueKeys0.KEY_T_REMODELING_EXTERNAL_EXAM_DETAIL_PRIMARY;
    public static final UniqueKey<RemodelingExternalPassbackBusinessRecord> KEY_T_REMODELING_EXTERNAL_PASSBACK_BUSINESS_PRIMARY = UniqueKeys0.KEY_T_REMODELING_EXTERNAL_PASSBACK_BUSINESS_PRIMARY;
    public static final UniqueKey<RemodelingExternalPassbackBusinessRecord> KEY_T_REMODELING_EXTERNAL_PASSBACK_BUSINESS_UNIQUE_REMODELING_EXTERNAL_PASSBACK_MEMBERIDSECTIONID = UniqueKeys0.KEY_T_REMODELING_EXTERNAL_PASSBACK_BUSINESS_UNIQUE_REMODELING_EXTERNAL_PASSBACK_MEMBERIDSECTIONID;
    public static final UniqueKey<RemodelingInternalCourseBusinessRecord> KEY_T_REMODELING_INTERNAL_COURSE_BUSINESS_PRIMARY = UniqueKeys0.KEY_T_REMODELING_INTERNAL_COURSE_BUSINESS_PRIMARY;
    public static final UniqueKey<RemodelingInternalCourseBusinessRecord> KEY_T_REMODELING_INTERNAL_COURSE_BUSINESS_UNIQUE_INTERNAL_COURSE_BUSINESS_COURSEID_REFERENCEID = UniqueKeys0.KEY_T_REMODELING_INTERNAL_COURSE_BUSINESS_UNIQUE_INTERNAL_COURSE_BUSINESS_COURSEID_REFERENCEID;
    public static final UniqueKey<RemodelingRoleDetailRecord> KEY_T_REMODELING_ROLE_DETAIL_PRIMARY = UniqueKeys0.KEY_T_REMODELING_ROLE_DETAIL_PRIMARY;
    public static final UniqueKey<RemodelingRoleIssueTimeConfigRecord> KEY_T_REMODELING_ROLE_ISSUE_TIME_CONFIG_PRIMARY = UniqueKeys0.KEY_T_REMODELING_ROLE_ISSUE_TIME_CONFIG_PRIMARY;
    public static final UniqueKey<RepeatCourseSectionStudyProgressRecord> KEY_T_REPEAT_COURSE_SECTION_STUDY_PROGRESS_PRIMARY = UniqueKeys0.KEY_T_REPEAT_COURSE_SECTION_STUDY_PROGRESS_PRIMARY;
    public static final UniqueKey<ShardingConfigRecord> KEY_T_SHARDING_CONFIG_PRIMARY = UniqueKeys0.KEY_T_SHARDING_CONFIG_PRIMARY;
    public static final UniqueKey<SplitLogConfigRecord> KEY_T_SPLIT_LOG_CONFIG_PRIMARY = UniqueKeys0.KEY_T_SPLIT_LOG_CONFIG_PRIMARY;
    public static final UniqueKey<SplitLogTimeRecord> KEY_T_SPLIT_LOG_TIME_PRIMARY = UniqueKeys0.KEY_T_SPLIT_LOG_TIME_PRIMARY;
    public static final UniqueKey<SplitTableConfigRecord> KEY_T_SPLIT_TABLE_CONFIG_PRIMARY = UniqueKeys0.KEY_T_SPLIT_TABLE_CONFIG_PRIMARY;
    public static final UniqueKey<SplitTableCountRecord> KEY_T_SPLIT_TABLE_COUNT_PRIMARY = UniqueKeys0.KEY_T_SPLIT_TABLE_COUNT_PRIMARY;
    public static final UniqueKey<SplitTableTimeRecord> KEY_T_SPLIT_TABLE_TIME_PRIMARY = UniqueKeys0.KEY_T_SPLIT_TABLE_TIME_PRIMARY;
    public static final UniqueKey<StudyActivityConfigRecord> KEY_T_STUDY_ACTIVITY_CONFIG_PRIMARY = UniqueKeys0.KEY_T_STUDY_ACTIVITY_CONFIG_PRIMARY;
    public static final UniqueKey<StudyExperienceRecord> KEY_T_STUDY_EXPERIENCE_PRIMARY = UniqueKeys0.KEY_T_STUDY_EXPERIENCE_PRIMARY;
    public static final UniqueKey<StudyPushAudienceObjectRecord> KEY_T_STUDY_PUSH_AUDIENCE_OBJECT_PRIMARY = UniqueKeys0.KEY_T_STUDY_PUSH_AUDIENCE_OBJECT_PRIMARY;
    public static final UniqueKey<StudyPushInfoRecord> KEY_T_STUDY_PUSH_INFO_PRIMARY = UniqueKeys0.KEY_T_STUDY_PUSH_INFO_PRIMARY;
    public static final UniqueKey<StudyPushObjectRecord> KEY_T_STUDY_PUSH_OBJECT_PRIMARY = UniqueKeys0.KEY_T_STUDY_PUSH_OBJECT_PRIMARY;
    public static final UniqueKey<StudyPushRecordRecord> KEY_T_STUDY_PUSH_RECORD_PRIMARY = UniqueKeys0.KEY_T_STUDY_PUSH_RECORD_PRIMARY;
    public static final UniqueKey<StudyPushShelvesRecord> KEY_T_STUDY_PUSH_SHELVES_PRIMARY = UniqueKeys0.KEY_T_STUDY_PUSH_SHELVES_PRIMARY;
    public static final UniqueKey<StudyRecord_2017Record> KEY_T_STUDY_RECORD_2017_PRIMARY = UniqueKeys0.KEY_T_STUDY_RECORD_2017_PRIMARY;
    public static final UniqueKey<StudyTaskRecord> KEY_T_STUDY_TASK_PRIMARY = UniqueKeys0.KEY_T_STUDY_TASK_PRIMARY;
    public static final UniqueKey<StudyTaskAttachmentRecord> KEY_T_STUDY_TASK_ATTACHMENT_PRIMARY = UniqueKeys0.KEY_T_STUDY_TASK_ATTACHMENT_PRIMARY;
    public static final UniqueKey<StudyTaskAuditMemberRecord> KEY_T_STUDY_TASK_AUDIT_MEMBER_PRIMARY = UniqueKeys0.KEY_T_STUDY_TASK_AUDIT_MEMBER_PRIMARY;
    public static final UniqueKey<SubjectAdvertisingRecord> KEY_T_SUBJECT_ADVERTISING_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_ADVERTISING_PRIMARY;
    public static final UniqueKey<SubjectCourseRecord> KEY_T_SUBJECT_COURSE_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_COURSE_PRIMARY;
    public static final UniqueKey<SubjectDirectionRecord> KEY_T_SUBJECT_DIRECTION_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_DIRECTION_PRIMARY;
    public static final UniqueKey<SubjectMemberBlacklistRecord> KEY_T_SUBJECT_MEMBER_BLACKLIST_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_MEMBER_BLACKLIST_PRIMARY;
    public static final UniqueKey<SubjectProblemRecord> KEY_T_SUBJECT_PROBLEM_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_PROBLEM_PRIMARY;
    public static final UniqueKey<SubjectRankRecord> KEY_T_SUBJECT_RANK_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_RANK_PRIMARY;
    public static final UniqueKey<SubjectRecommendRecord> KEY_T_SUBJECT_RECOMMEND_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_RECOMMEND_PRIMARY;
    public static final UniqueKey<SubjectRoleCommentRecord> KEY_T_SUBJECT_ROLE_COMMENT_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_ROLE_COMMENT_PRIMARY;
    public static final UniqueKey<SubjectRoleDetailRecord> KEY_T_SUBJECT_ROLE_DETAIL_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_ROLE_DETAIL_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogAhRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_AH_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_AH_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogBjRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_BJ_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_BJ_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogCmRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_CM_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_CM_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogCqRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_CQ_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_CQ_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogEbRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_EB_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_EB_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogFjRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_FJ_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_FJ_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogGdRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_GD_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_GD_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogGsRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_GS_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_GS_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogGxRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_GX_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_GX_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogGzRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_GZ_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_GZ_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogHbRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_HB_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_HB_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogHlRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_HL_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_HL_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogHnRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_HN_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_HN_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogJlRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_JL_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_JL_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogJsRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_JS_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_JS_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogJxRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_JX_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_JX_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogLnRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_LN_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_LN_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogNmRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_NM_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_NM_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogNxRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_NX_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_NX_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogOtherRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_OTHER_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_OTHER_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogQhRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_QH_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_QH_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogQoRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_QO_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_QO_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogScRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_SC_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_SC_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogSdRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_SD_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_SD_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogShRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_SH_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_SH_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogSnRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_SN_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_SN_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogSxRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_SX_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_SX_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogTjRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_TJ_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_TJ_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogXjRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_XJ_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_XJ_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogXnRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_XN_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_XN_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogXzRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_XZ_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_XZ_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogYnRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_YN_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_YN_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogZgttRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_ZGTT_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_ZGTT_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogZjRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_ZJ_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_ZJ_PRIMARY;
    public static final UniqueKey<SubjectSectionStudyLogZxRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_ZX_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_SECTION_STUDY_LOG_ZX_PRIMARY;
    public static final UniqueKey<SubjectStudyDayExceptionRecord> KEY_T_SUBJECT_STUDY_DAY_EXCEPTION_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_DAY_EXCEPTION_PRIMARY;
    public static final UniqueKey<SubjectStudyLogAhDayRecord> KEY_T_SUBJECT_STUDY_LOG_AH_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_AH_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogBjDayRecord> KEY_T_SUBJECT_STUDY_LOG_BJ_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_BJ_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogCmDayRecord> KEY_T_SUBJECT_STUDY_LOG_CM_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_CM_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogCqDayRecord> KEY_T_SUBJECT_STUDY_LOG_CQ_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_CQ_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogEbDayRecord> KEY_T_SUBJECT_STUDY_LOG_EB_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_EB_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogExceptionRecord> KEY_T_SUBJECT_STUDY_LOG_EXCEPTION_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_EXCEPTION_PRIMARY;
    public static final UniqueKey<SubjectStudyLogFjDayRecord> KEY_T_SUBJECT_STUDY_LOG_FJ_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_FJ_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogGdDayRecord> KEY_T_SUBJECT_STUDY_LOG_GD_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_GD_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogGsDayRecord> KEY_T_SUBJECT_STUDY_LOG_GS_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_GS_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogGxDayRecord> KEY_T_SUBJECT_STUDY_LOG_GX_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_GX_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogGzDayRecord> KEY_T_SUBJECT_STUDY_LOG_GZ_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_GZ_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogHbDayRecord> KEY_T_SUBJECT_STUDY_LOG_HB_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_HB_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogHlDayRecord> KEY_T_SUBJECT_STUDY_LOG_HL_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_HL_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogHnDayRecord> KEY_T_SUBJECT_STUDY_LOG_HN_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_HN_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogJlDayRecord> KEY_T_SUBJECT_STUDY_LOG_JL_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_JL_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogJsDayRecord> KEY_T_SUBJECT_STUDY_LOG_JS_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_JS_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogJxDayRecord> KEY_T_SUBJECT_STUDY_LOG_JX_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_JX_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogLnDayRecord> KEY_T_SUBJECT_STUDY_LOG_LN_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_LN_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogNmDayRecord> KEY_T_SUBJECT_STUDY_LOG_NM_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_NM_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogNxDayRecord> KEY_T_SUBJECT_STUDY_LOG_NX_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_NX_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogOtherDayRecord> KEY_T_SUBJECT_STUDY_LOG_OTHER_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_OTHER_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogQhDayRecord> KEY_T_SUBJECT_STUDY_LOG_QH_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_QH_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogQoDayRecord> KEY_T_SUBJECT_STUDY_LOG_QO_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_QO_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogScDayRecord> KEY_T_SUBJECT_STUDY_LOG_SC_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_SC_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogSdDayRecord> KEY_T_SUBJECT_STUDY_LOG_SD_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_SD_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogShDayRecord> KEY_T_SUBJECT_STUDY_LOG_SH_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_SH_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogSnDayRecord> KEY_T_SUBJECT_STUDY_LOG_SN_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_SN_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogSxDayRecord> KEY_T_SUBJECT_STUDY_LOG_SX_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_SX_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogTjDayRecord> KEY_T_SUBJECT_STUDY_LOG_TJ_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_TJ_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogXjDayRecord> KEY_T_SUBJECT_STUDY_LOG_XJ_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_XJ_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogXnDayRecord> KEY_T_SUBJECT_STUDY_LOG_XN_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_XN_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogXzDayRecord> KEY_T_SUBJECT_STUDY_LOG_XZ_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_XZ_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogYnDayRecord> KEY_T_SUBJECT_STUDY_LOG_YN_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_YN_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogZgttDayRecord> KEY_T_SUBJECT_STUDY_LOG_ZGTT_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_ZGTT_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogZjDayRecord> KEY_T_SUBJECT_STUDY_LOG_ZJ_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_ZJ_DAY_PRIMARY;
    public static final UniqueKey<SubjectStudyLogZxDayRecord> KEY_T_SUBJECT_STUDY_LOG_ZX_DAY_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_STUDY_LOG_ZX_DAY_PRIMARY;
    public static final UniqueKey<SubjectTextAreaRecord> KEY_T_SUBJECT_TEXT_AREA_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_TEXT_AREA_PRIMARY;
    public static final UniqueKey<SubjectYearBillRecord> KEY_T_SUBJECT_YEAR_BILL_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_YEAR_BILL_PRIMARY;
    public static final UniqueKey<SummaryRecord> KEY_T_SUMMARY_PRIMARY = UniqueKeys0.KEY_T_SUMMARY_PRIMARY;
    public static final UniqueKey<SupplierRecord> KEY_T_SUPPLIER_PRIMARY = UniqueKeys0.KEY_T_SUPPLIER_PRIMARY;
    public static final UniqueKey<TempMemberRecord> KEY_T_TEMP_MEMBER_PRIMARY = UniqueKeys0.KEY_T_TEMP_MEMBER_PRIMARY;
    public static final UniqueKey<TempRepairCourseRecord> KEY_T_TEMP_REPAIR_COURSE_PRIMARY = UniqueKeys0.KEY_T_TEMP_REPAIR_COURSE_PRIMARY;
    public static final UniqueKey<TempRepairSubjectRecord> KEY_T_TEMP_REPAIR_SUBJECT_PRIMARY = UniqueKeys0.KEY_T_TEMP_REPAIR_SUBJECT_PRIMARY;
    public static final UniqueKey<TempSectionStudyLogGt_24Record> KEY_T_TEMP_SECTION_STUDY_LOG_GT_24_PRIMARY = UniqueKeys0.KEY_T_TEMP_SECTION_STUDY_LOG_GT_24_PRIMARY;
    public static final UniqueKey<TempSubjectSectionStudyLogRecord> KEY_T_TEMP_SUBJECT_SECTION_STUDY_LOG_PRIMARY = UniqueKeys0.KEY_T_TEMP_SUBJECT_SECTION_STUDY_LOG_PRIMARY;
    public static final UniqueKey<ThematicRecord> KEY_T_THEMATIC_PRIMARY = UniqueKeys0.KEY_T_THEMATIC_PRIMARY;
    public static final UniqueKey<ThematicAttachmentRecord> KEY_T_THEMATIC_ATTACHMENT_PRIMARY = UniqueKeys0.KEY_T_THEMATIC_ATTACHMENT_PRIMARY;
    public static final UniqueKey<ThematicChapterRecord> KEY_T_THEMATIC_CHAPTER_PRIMARY = UniqueKeys0.KEY_T_THEMATIC_CHAPTER_PRIMARY;
    public static final UniqueKey<ThematicChapterSectionRecord> KEY_T_THEMATIC_CHAPTER_SECTION_PRIMARY = UniqueKeys0.KEY_T_THEMATIC_CHAPTER_SECTION_PRIMARY;
    public static final UniqueKey<ThematicMemberRecord> KEY_T_THEMATIC_MEMBER_PRIMARY = UniqueKeys0.KEY_T_THEMATIC_MEMBER_PRIMARY;
    public static final UniqueKey<ThematicNoticeRecord> KEY_T_THEMATIC_NOTICE_PRIMARY = UniqueKeys0.KEY_T_THEMATIC_NOTICE_PRIMARY;
    public static final UniqueKey<ThematicWorkRecord> KEY_T_THEMATIC_WORK_PRIMARY = UniqueKeys0.KEY_T_THEMATIC_WORK_PRIMARY;
    public static final UniqueKey<ThirdPartyCallRecordRecord> KEY_T_THIRD_PARTY_CALL_RECORD_PRIMARY = UniqueKeys0.KEY_T_THIRD_PARTY_CALL_RECORD_PRIMARY;
    public static final UniqueKey<ThirdPartyCourseInfoRecord> KEY_T_THIRD_PARTY_COURSE_INFO_PRIMARY = UniqueKeys0.KEY_T_THIRD_PARTY_COURSE_INFO_PRIMARY;
    public static final UniqueKey<ThirdPartyCourseStudyProgressRecord> KEY_T_THIRD_PARTY_COURSE_STUDY_PROGRESS_PRIMARY = UniqueKeys0.KEY_T_THIRD_PARTY_COURSE_STUDY_PROGRESS_PRIMARY;
    public static final UniqueKey<TopicRecord> KEY_T_TOPIC_PRIMARY = UniqueKeys0.KEY_T_TOPIC_PRIMARY;
    public static final UniqueKey<TopicRecord> KEY_T_TOPIC_UNIQUE_T_TOPIC_F_NAME_DELETE_FLAG = UniqueKeys0.KEY_T_TOPIC_UNIQUE_T_TOPIC_F_NAME_DELETE_FLAG;
    public static final UniqueKey<TopicObjectRecord> KEY_T_TOPIC_OBJECT_PRIMARY = UniqueKeys0.KEY_T_TOPIC_OBJECT_PRIMARY;
    public static final UniqueKey<WhiteRecordRecord> KEY_T_WHITE_RECORD_PRIMARY = UniqueKeys0.KEY_T_WHITE_RECORD_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressFfclc_2022Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressFfclc_2022Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022_UNIQUE_T_CSSP_FFCLC_2022_SECTION_MEMBER = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022_UNIQUE_T_CSSP_FFCLC_2022_SECTION_MEMBER;
    public static final UniqueKey<CourseInfoDjypRecord> KEY_T_COURSE_INFO_DJYP_PRIMARY = UniqueKeys0.KEY_T_COURSE_INFO_DJYP_PRIMARY;
    public static final UniqueKey<CourseStudyPlanConfigRecord> KEY_T_COURSE_STUDY_PLAN_CONFIG_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PLAN_CONFIG_PRIMARY;

    public static final UniqueKey<MultidimensionalScoringRecord> KEY_T_MULTIDIMENSIONAL_SCORING_PRIMARY = UniqueKeys0.KEY_T_MULTIDIMENSIONAL_SCORING_PRIMARY;
    public static final UniqueKey<MultidimensionalScoringSubjectRecord> KEY_T_MULTIDIMENSIONAL_SCORING_SUBJECT_PRIMARY = UniqueKeys0.KEY_T_MULTIDIMENSIONAL_SCORING_SUBJECT_PRIMARY;
    public static final UniqueKey<MultidimensionalStudentScoreSheet_00Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_00_PRIMARY = UniqueKeys0.KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_00_PRIMARY;
    public static final UniqueKey<MultidimensionalStudentScoreSheet_01Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_01_PRIMARY = UniqueKeys0.KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_01_PRIMARY;
    public static final UniqueKey<MultidimensionalStudentScoreSheet_02Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_02_PRIMARY = UniqueKeys0.KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_02_PRIMARY;
    public static final UniqueKey<MultidimensionalStudentScoreSheet_03Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_03_PRIMARY = UniqueKeys0.KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_03_PRIMARY;
    public static final UniqueKey<MultidimensionalStudentScoreSheet_04Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_04_PRIMARY = UniqueKeys0.KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_04_PRIMARY;
    public static final UniqueKey<MultidimensionalStudentScoreSheet_05Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_05_PRIMARY = UniqueKeys0.KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_05_PRIMARY;
    public static final UniqueKey<MultidimensionalStudentScoreSheet_06Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_06_PRIMARY = UniqueKeys0.KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_06_PRIMARY;
    public static final UniqueKey<MultidimensionalStudentScoreSheet_07Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_07_PRIMARY = UniqueKeys0.KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_07_PRIMARY;
    public static final UniqueKey<MultidimensionalStudentScoreSheet_08Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_08_PRIMARY = UniqueKeys0.KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_08_PRIMARY;
    public static final UniqueKey<MultidimensionalStudentScoreSheet_09Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09_PRIMARY = UniqueKeys0.KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09_PRIMARY;
    public static final UniqueKey<CourseRedShipAuditDetailRecord> KEY_T_COURSE_RED_SHIP_AUDIT_DETAIL_PRIMARY = UniqueKeys0.KEY_T_COURSE_RED_SHIP_AUDIT_DETAIL_PRIMARY;
    public static final UniqueKey<CourseRedShipAuditVersionRecord> KEY_T_COURSE_RED_SHIP_AUDIT_VERSION_PRIMARY = UniqueKeys0.KEY_T_COURSE_RED_SHIP_AUDIT_VERSION_PRIMARY;
    public static final UniqueKey<CourseRedShipAuditChapterSectionRecord> KEY_T_COURSE_RED_SHIP_AUDIT_CHAPTER_SECTION_PRIMARY = UniqueKeys0.KEY_T_COURSE_RED_SHIP_AUDIT_CHAPTER_SECTION_PRIMARY;
    public static final UniqueKey<IntelligentBroadcastRecord> KEY_T_INTELLIGENT_BROADCAST_PRIMARY = UniqueKeys0.KEY_T_INTELLIGENT_BROADCAST_PRIMARY;

    public static final UniqueKey<CourseStudyProgressArchived_00Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_00_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_00_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_00Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_00_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_00_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_01Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_01_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_01_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_01Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_01_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_01_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_02Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_02_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_02_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_02Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_02_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_02_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_03Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_03_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_03_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_03Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_03_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_03_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_04Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_04_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_04_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_04Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_04_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_04_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_05Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_05_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_05_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_05Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_05_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_05_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_06Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_06_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_06_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_06Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_06_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_06_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_07Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_07_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_07_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_07Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_07_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_07_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_08Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_08_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_08_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_08Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_08_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_08_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_09Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_09_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_09_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_09Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_09_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_09_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_10Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_10_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_10_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_10Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_10_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_10_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_11Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_11_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_11_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_11Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_11_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_11_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_12Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_12_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_12_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_12Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_12_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_12_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_13Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_13_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_13_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_13Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_13_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_13_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_14Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_14_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_14_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_14Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_14_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_14_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_15Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_15_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_15_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_15Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_15_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_15_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_16Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_16_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_16_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_16Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_16_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_16_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_17Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_17_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_17_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_17Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_17_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_17_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_18Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_18_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_18_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_18Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_18_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_18_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<CourseStudyProgressArchived_19Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_19_PRIMARY = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_19_PRIMARY;
    public static final UniqueKey<CourseStudyProgressArchived_19Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_19_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = UniqueKeys0.KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_19_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE;
    public static final UniqueKey<DeleteDataCourseRecord> KEY_T_DELETE_DATA_COURSE_PRIMARY = UniqueKeys0.KEY_T_DELETE_DATA_COURSE_PRIMARY;
    public static final UniqueKey<IntelligentNoteRecord> KEY_T_INTELLIGENT_NOTE_PRIMARY = UniqueKeys0.KEY_T_INTELLIGENT_NOTE_PRIMARY;
    public static final UniqueKey<IntelligentNoteRecord> KEY_T_INTELLIGENT_NOTE_IDX_COURSE_SECTION_MEMBER = UniqueKeys0.KEY_T_INTELLIGENT_NOTE_IDX_COURSE_SECTION_MEMBER;
    public static final UniqueKey<IntelligentNoteBookmarkRecord> KEY_T_INTELLIGENT_NOTE_BOOKMARK_PRIMARY = UniqueKeys0.KEY_T_INTELLIGENT_NOTE_BOOKMARK_PRIMARY;
    public static final UniqueKey<CaptionRecord> KEY_T_CAPTION_PRIMARY = UniqueKeys0.KEY_T_CAPTION_PRIMARY;
    public static final UniqueKey<SubjectTopicManagerRecord> KEY_T_SUBJECT_TOPIC_MANAGER_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_TOPIC_MANAGER_PRIMARY;

    public static final UniqueKey<CourseSectionStudyProgressAhRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_AH_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_AH_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressAhRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_AH_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_AH_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressBjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_BJ_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_BJ_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressBjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_BJ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_BJ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressCmRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_CM_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_CM_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressCmRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_CM_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_CM_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressCqRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_CQ_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_CQ_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressCqRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_CQ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_CQ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressEbRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_EB_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_EB_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressEbRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_EB_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_EB_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressFjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FJ_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_FJ_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressFjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FJ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_FJ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressGdRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_GD_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_GD_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressGdRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_GD_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_GD_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressGsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_GS_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_GS_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressGsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_GS_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_GS_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressGxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_GX_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_GX_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressGxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_GX_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_GX_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressGzRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_GZ_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_GZ_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressGzRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_GZ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_GZ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressHbRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_HB_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_HB_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressHbRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_HB_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_HB_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressHlRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_HL_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_HL_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressHlRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_HL_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_HL_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressHnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_HN_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_HN_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressHnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_HN_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_HN_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressJlRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_JL_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_JL_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressJlRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_JL_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_JL_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressJsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_JS_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_JS_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressJsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_JS_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_JS_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressJxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_JX_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_JX_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressJxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_JX_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_JX_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressLnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_LN_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_LN_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressLnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_LN_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_LN_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressNmRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_NM_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_NM_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressNmRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_NM_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_NM_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressNxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_NX_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_NX_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressNxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_NX_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_NX_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressOtherRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_OTHER_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_OTHER_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressOtherRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_OTHER_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_OTHER_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressQhRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_QH_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_QH_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressQhRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_QH_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_QH_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressQoRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_QO_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_QO_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressQoRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_QO_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_QO_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressScRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SC_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_SC_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressScRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SC_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_SC_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressSdRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SD_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_SD_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressSdRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SD_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_SD_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressShRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SH_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_SH_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressShRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SH_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_SH_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressSnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SN_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_SN_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressSnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SN_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_SN_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressSxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SX_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_SX_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressSxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SX_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_SX_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressTjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_TJ_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_TJ_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressTjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_TJ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_TJ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressXjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XJ_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_XJ_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressXjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XJ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_XJ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressXnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XN_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_XN_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressXnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XN_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_XN_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressXzRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XZ_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_XZ_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressXzRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XZ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_XZ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressYnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_YN_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_YN_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressYnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_YN_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_YN_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressZgttRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZGTT_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZGTT_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressZgttRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZGTT_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZGTT_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressZjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZJ_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZJ_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressZjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZJ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZJ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<CourseSectionStudyProgressZxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZX_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZX_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressZxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZX_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZX_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION;
    public static final UniqueKey<AnnualBill_2022Record> KEY_T_ANNUAL_BILL_2022_PRIMARY = UniqueKeys0.KEY_T_ANNUAL_BILL_2022_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressFfclc_2023Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressFfclc_2023Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023_UNIQUE_T_CSSP_FFCLC_2023_SECTION_MEMBER = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023_UNIQUE_T_CSSP_FFCLC_2023_SECTION_MEMBER;
    public static final UniqueKey<CourseSectionStudyProgressFfcls_2023Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_2023_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_2023_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressFfcls_2023Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_2023_UNIQUE_T_CSSP_FFCLS_2023_SECTION_MEMBER = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_2023_UNIQUE_T_CSSP_FFCLS_2023_SECTION_MEMBER;
    public static final UniqueKey<AuthenticatedGroupRecord> KEY_T_AUTHENTICATED_GROUP_PRIMARY = UniqueKeys0.KEY_T_AUTHENTICATED_GROUP_PRIMARY;
    public static final UniqueKey<AuthenticatedZoneRecord> KEY_T_AUTHENTICATED_ZONE_PRIMARY = UniqueKeys0.KEY_T_AUTHENTICATED_ZONE_PRIMARY;
    public static final UniqueKey<AuthenticatedZoneGroupRecord> KEY_T_AUTHENTICATED_ZONE_GROUP_PRIMARY = UniqueKeys0.KEY_T_AUTHENTICATED_ZONE_GROUP_PRIMARY;
    public static final UniqueKey<SubAuthenticatedRecord> KEY_T_SUB_AUTHENTICATED_PRIMARY = UniqueKeys0.KEY_T_SUB_AUTHENTICATED_PRIMARY;
    public static final UniqueKey<SubAuthenticatedCertificateRecordRecord> KEY_T_SUB_AUTHENTICATED_CERTIFICATE_RECORD_PRIMARY = UniqueKeys0.KEY_T_SUB_AUTHENTICATED_CERTIFICATE_RECORD_PRIMARY;
    public static final UniqueKey<SubAuthenticatedContentConfigureRecord> KEY_T_SUB_AUTHENTICATED_CONTENT_CONFIGURE_PRIMARY = UniqueKeys0.KEY_T_SUB_AUTHENTICATED_CONTENT_CONFIGURE_PRIMARY;
    public static final UniqueKey<SubAuthenticatedContentConfigureRecord> KEY_T_SUB_AUTHENTICATED_CONTENT_CONFIGURE_T_SUB_AUTHENTICATED_CONTENT_CONFIG_CONTENT_IDX = UniqueKeys0.KEY_T_SUB_AUTHENTICATED_CONTENT_CONFIGURE_T_SUB_AUTHENTICATED_CONTENT_CONFIG_CONTENT_IDX;
    public static final UniqueKey<SubAuthenticatedCourseProgressRecord> KEY_T_SUB_AUTHENTICATED_COURSE_PROGRESS_PRIMARY = UniqueKeys0.KEY_T_SUB_AUTHENTICATED_COURSE_PROGRESS_PRIMARY;
    public static final UniqueKey<SubAuthenticatedResourceAuditRecordRecord> KEY_T_SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD_PRIMARY = UniqueKeys0.KEY_T_SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD_PRIMARY;
    public static final UniqueKey<SubAuthenticatedStudyOnlineRecord> KEY_T_SUB_AUTHENTICATED_STUDY_ONLINE_PRIMARY = UniqueKeys0.KEY_T_SUB_AUTHENTICATED_STUDY_ONLINE_PRIMARY;
    public static final UniqueKey<SubAuthenticatedDimensionRecord> KEY_T_SUB_AUTHENTICATED_DIMENSION_PRIMARY = UniqueKeys0.KEY_T_SUB_AUTHENTICATED_DIMENSION_PRIMARY;
    public static final UniqueKey<SubAuthenticatedMemberDimensionRecord> KEY_T_SUB_AUTHENTICATED_MEMBER_DIMENSION_PRIMARY = UniqueKeys0.KEY_T_SUB_AUTHENTICATED_MEMBER_DIMENSION_PRIMARY;
    public static final UniqueKey<SubAuthenticatedRegisterRecord> KEY_T_SUB_AUTHENTICATED_REGISTER_PRIMARY = UniqueKeys0.KEY_T_SUB_AUTHENTICATED_REGISTER_PRIMARY;


    public static final UniqueKey<BusinessEmergencyRecord> KEY_T_BUSINESS_EMERGENCY_PRIMARY = UniqueKeys0.KEY_T_BUSINESS_EMERGENCY_PRIMARY;
    public static final UniqueKey<ReportRedlistRecord> KEY_T_REPORT_REDLIST_PRIMARY = UniqueKeys0.KEY_T_REPORT_REDLIST_PRIMARY;


    public static final UniqueKey<GenseeSignInRecord> KEY_T_GENSEE_SIGN_IN_PRIMARY = UniqueKeys0.KEY_T_GENSEE_SIGN_IN_PRIMARY;



    public static final UniqueKey<IndividualShortVideoDetailsRecord> KEY_T_INDIVIDUAL_SHORT_VIDEO_DETAILS_PRIMARY = UniqueKeys0.KEY_T_INDIVIDUAL_SHORT_VIDEO_DETAILS_PRIMARY;
    public static final UniqueKey<IndividualShortVideoSummaryRecord> KEY_T_INDIVIDUAL_SHORT_VIDEO_SUMMARY_PRIMARY = UniqueKeys0.KEY_T_INDIVIDUAL_SHORT_VIDEO_SUMMARY_PRIMARY;
    public static final UniqueKey<PersonalShortVideoLearningDetailsRecord> KEY_T_PERSONAL_SHORT_VIDEO_LEARNING_DETAILS_PRIMARY = UniqueKeys0.KEY_T_PERSONAL_SHORT_VIDEO_LEARNING_DETAILS_PRIMARY;
    public static final UniqueKey<ShortVideoCreateDetailsRecord> KEY_T_SHORT_VIDEO_CREATE_DETAILS_PRIMARY = UniqueKeys0.KEY_T_SHORT_VIDEO_CREATE_DETAILS_PRIMARY;
    public static final UniqueKey<ShortVideoReportRecord> KEY_T_SHORT_VIDEO_REPORT_PRIMARY = UniqueKeys0.KEY_T_SHORT_VIDEO_REPORT_PRIMARY;

    public static final UniqueKey<MiguUserAccessRecord> KEY_T_MIGU_USER_ACCESS_PRIMARY = UniqueKeys0.KEY_T_MIGU_USER_ACCESS_PRIMARY;
    public static final UniqueKey<MiguAttachmentRecord> KEY_T_MIGU_ATTACHMENT_PRIMARY = UniqueKeys0.KEY_T_MIGU_ATTACHMENT_PRIMARY;

    public static final UniqueKey<CourseMarkRecord> KEY_T_COURSE_MARK_PRIMARY = UniqueKeys0.KEY_T_COURSE_MARK_PRIMARY;
    public static final UniqueKey<LiveVirtualSpaceRecord> KEY_T_LIVE_VIRTUAL_SPACE_PRIMARY = UniqueKeys0.KEY_T_LIVE_VIRTUAL_SPACE_PRIMARY;

    public static final UniqueKey<NotePraiseRecord> KEY_T_NOTE_PRAISE_PRIMARY = UniqueKeys0.KEY_T_NOTE_PRAISE_PRIMARY;

    public static final UniqueKey<GenseeShareRecord> KEY_T_GENSEE_SHARE_PRIMARY = UniqueKeys0.KEY_T_GENSEE_SHARE_PRIMARY;
    public static final UniqueKey<KnowledgeRedShipAuditRecord> KEY_T_KNOWLEDGE_RED_SHIP_AUDIT_PRIMARY = UniqueKeys0.KEY_T_KNOWLEDGE_RED_SHIP_AUDIT_PRIMARY;

    public static final UniqueKey<ShortVideoLogDayRecord> KEY_T_SHORT_VIDEO_LOG_DAY_PRIMARY = UniqueKeys0.KEY_T_SHORT_VIDEO_LOG_DAY_PRIMARY;
    public static final UniqueKey<AnnualBill_2023Record> KEY_T_ANNUAL_BILL_2023_PRIMARY = UniqueKeys0.KEY_T_ANNUAL_BILL_2023_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressFfclc_2024Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_2024_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_2024_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressFfclc_2024Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_2024_UNIQUE_T_CSSP_FFCLC_2024_SECTION_MEMBER = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_2024_UNIQUE_T_CSSP_FFCLC_2024_SECTION_MEMBER;
    public static final UniqueKey<CourseSectionStudyProgressFfcls_2024Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024_PRIMARY = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024_PRIMARY;
    public static final UniqueKey<CourseSectionStudyProgressFfcls_2024Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024_UNIQUE_T_CSSP_FFCLS_2024_SECTION_MEMBER = UniqueKeys0.KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024_UNIQUE_T_CSSP_FFCLS_2024_SECTION_MEMBER;
    public static final UniqueKey<DjLogRecord> KEY_T_DJ_LOG_PRIMARY = UniqueKeys0.KEY_T_DJ_LOG_PRIMARY;

    public static final UniqueKey<SignInRecord> KEY_T_SIGN_IN_PRIMARY = UniqueKeys0.KEY_T_SIGN_IN_PRIMARY;
    public static final UniqueKey<BroadcastCountRecord> KEY_T_BROADCAST_COUNT_PRIMARY = UniqueKeys0.KEY_T_BROADCAST_COUNT_PRIMARY;
    public static final UniqueKey<MemberCourseHoursRecord> KEY_T_MEMBER_COURSE_HOURS_PRIMARY = UniqueKeys0.KEY_T_MEMBER_COURSE_HOURS_PRIMARY;

    public static final UniqueKey<PartyBannerRecord> KEY_T_PARTY_BANNER_PRIMARY = UniqueKeys0.KEY_T_PARTY_BANNER_PRIMARY;
    public static final UniqueKey<PartyDemonstrationTrainingConfigRecord> KEY_T_PARTY_DEMONSTRATION_TRAINING_CONFIG_PRIMARY = UniqueKeys0.KEY_T_PARTY_DEMONSTRATION_TRAINING_CONFIG_PRIMARY;
    public static final UniqueKey<PartyFocusConfigRecord> KEY_T_PARTY_FOCUS_CONFIG_PRIMARY = UniqueKeys0.KEY_T_PARTY_FOCUS_CONFIG_PRIMARY;
    public static final UniqueKey<PartyDynamicNewsConfigRecord> KEY_T_PARTY_DYNAMIC_NEWS_CONFIG_PRIMARY = UniqueKeys0.KEY_T_PARTY_DYNAMIC_NEWS_CONFIG_PRIMARY;
    public static final UniqueKey<AiQuestionRecord> KEY_T_AI_QUESTION_PRIMARY = UniqueKeys0.KEY_T_AI_QUESTION_PRIMARY;
    public static final UniqueKey<AiAnswerRecord> KEY_T_AI_ANSWER_PRIMARY = UniqueKeys0.KEY_T_AI_ANSWER_PRIMARY;
    public static final UniqueKey<AiFeedbackRecord> KEY_T_AI_FEEDBACK_PRIMARY = UniqueKeys0.KEY_T_AI_FEEDBACK_PRIMARY;
    public static final UniqueKey<AiMentorRecord> KEY_T_AI_MENTOR_PRIMARY = UniqueKeys0.KEY_T_AI_MENTOR_PRIMARY;
    public static final UniqueKey<AiPresetExampleRecord> KEY_T_AI_PRESET_EXAMPLE_PRIMARY = UniqueKeys0.KEY_T_AI_PRESET_EXAMPLE_PRIMARY;


    public static final UniqueKey<GbCourseAuditRecord> KEY_T_GB_COURSE_AUDIT_PRIMARY = UniqueKeys0.KEY_T_GB_COURSE_AUDIT_PRIMARY;
    public static final UniqueKey<GbCourseClassificationRecord> KEY_T_GB_COURSE_CLASSIFICATION_PRIMARY = UniqueKeys0.KEY_T_GB_COURSE_CLASSIFICATION_PRIMARY;
    public static final UniqueKey<GbCourseConfigurationRecord> KEY_T_GB_COURSE_CONFIGURATION_PRIMARY = UniqueKeys0.KEY_T_GB_COURSE_CONFIGURATION_PRIMARY;
    public static final UniqueKey<GbCourseLibraryRecord> KEY_T_GB_COURSE_LIBRARY_PRIMARY = UniqueKeys0.KEY_T_GB_COURSE_LIBRARY_PRIMARY;
    public static final UniqueKey<GbCourseMiddleRecord> KEY_T_GB_COURSE_MIDDLE_PRIMARY = UniqueKeys0.KEY_T_GB_COURSE_MIDDLE_PRIMARY;
    public static final UniqueKey<GbCourseRecordRecord> KEY_T_GB_COURSE_RECORD_PRIMARY = UniqueKeys0.KEY_T_GB_COURSE_RECORD_PRIMARY;
    public static final UniqueKey<GbLecturerLibraryRecord> KEY_T_GB_LECTURER_LIBRARY_PRIMARY = UniqueKeys0.KEY_T_GB_LECTURER_LIBRARY_PRIMARY;
    public static final UniqueKey<GbMemberRecord> KEY_T_GB_MEMBER_PRIMARY = UniqueKeys0.KEY_T_GB_MEMBER_PRIMARY;
    public static final UniqueKey<SubjectPlanRelatedRecord> KEY_T_SUBJECT_PLAN_RELATED_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_PLAN_RELATED_PRIMARY;

    public static final UniqueKey<CoursewareNoteRecord> KEY_T_COURSEWARE_NOTE_PRIMARY = UniqueKeys0.KEY_T_COURSEWARE_NOTE_PRIMARY;
    public static final UniqueKey<CoursewareNoteAuditRecord> KEY_T_COURSEWARE_NOTE_AUDIT_PRIMARY = UniqueKeys0.KEY_T_COURSEWARE_NOTE_AUDIT_PRIMARY;
    public static final UniqueKey<CoursewareNoteVersionRecord> KEY_T_COURSEWARE_NOTE_VERSION_PRIMARY = UniqueKeys0.KEY_T_COURSEWARE_NOTE_VERSION_PRIMARY;

    public static final UniqueKey<CourseMainNoteRecord> KEY_T_COURSE_MAIN_NOTE_PRIMARY = UniqueKeys0.KEY_T_COURSE_MAIN_NOTE_PRIMARY;
    public static final UniqueKey<CourseMainNoteVersionRecord> KEY_T_COURSE_MAIN_NOTE_VERSION_PRIMARY = UniqueKeys0.KEY_T_COURSE_MAIN_NOTE_VERSION_PRIMARY;
    public static final UniqueKey<CourseMainNoteAuditRecord> KEY_T_COURSE_MAIN_NOTE_AUDIT_PRIMARY = UniqueKeys0.KEY_T_COURSE_MAIN_NOTE_AUDIT_PRIMARY;
    public static final UniqueKey<ShortVideoOperationRecord> KEY_T_SHORT_VIDEO_OPERATION_PRIMARY = UniqueKeys0.KEY_T_SHORT_VIDEO_OPERATION_PRIMARY;
    public static final UniqueKey<ShortVideoOperationGroupRecord> KEY_T_SHORT_VIDEO_OPERATION_GROUP_PRIMARY = UniqueKeys0.KEY_T_SHORT_VIDEO_OPERATION_GROUP_PRIMARY;
    public static final UniqueKey<ShortVideoOperationIntegralRecord> KEY_T_SHORT_VIDEO_OPERATION_INTEGRAL_PRIMARY = UniqueKeys0.KEY_T_SHORT_VIDEO_OPERATION_INTEGRAL_PRIMARY;
    public static final UniqueKey<ShortVideoOperationMemberRecord> KEY_T_SHORT_VIDEO_OPERATION_MEMBER_PRIMARY = UniqueKeys0.KEY_T_SHORT_VIDEO_OPERATION_MEMBER_PRIMARY;
    public static final UniqueKey<ShortVideoOperationPickQuestionRecord> KEY_T_SHORT_VIDEO_OPERATION_PICK_QUESTION_PRIMARY = UniqueKeys0.KEY_T_SHORT_VIDEO_OPERATION_PICK_QUESTION_PRIMARY;
    public static final UniqueKey<ShortVideoOperationQuestionRecord> KEY_T_SHORT_VIDEO_OPERATION_QUESTION_PRIMARY = UniqueKeys0.KEY_T_SHORT_VIDEO_OPERATION_QUESTION_PRIMARY;
    public static final UniqueKey<IshowSdkRequestRecord> KEY_T_ISHOW_SDK_REQUEST_PRIMARY = UniqueKeys0.KEY_T_ISHOW_SDK_REQUEST_PRIMARY;
    public static final UniqueKey<IshowStudyRecordRecord> KEY_T_ISHOW_STUDY_RECORD_PRIMARY = UniqueKeys0.KEY_T_ISHOW_STUDY_RECORD_PRIMARY;
    public static final UniqueKey<StudyReportAnalysisManagersRecord> KEY_T_STUDY_REPORT_ANALYSIS_MANAGERS_PRIMARY = UniqueKeys0.KEY_T_STUDY_REPORT_ANALYSIS_MANAGERS_PRIMARY;

    public static final UniqueKey<SubjectRankYearRecord> KEY_T_SUBJECT_RANK_YEAR_PRIMARY = UniqueKeys0.KEY_T_SUBJECT_RANK_YEAR_PRIMARY;
    public static final UniqueKey<CourseRenRecord> KEY_T_COURSE_REN_PRIMARY = UniqueKeys0.KEY_T_COURSE_REN_PRIMARY;
    public static final UniqueKey<CourseRenFeedbackRecord> KEY_T_COURSE_REN_FEEDBACK_PRIMARY = UniqueKeys0.KEY_T_COURSE_REN_FEEDBACK_PRIMARY;


    public static final UniqueKey<ApplicationConfigRecord> KEY_T_APPLICATION_CONFIG_PRIMARY = UniqueKeys0.KEY_T_APPLICATION_CONFIG_PRIMARY;
    public static final UniqueKey<AbilityRecord> KEY_T_ABILITY_PRIMARY = UniqueKeys0.KEY_T_ABILITY_PRIMARY;
    public static final UniqueKey<AbilityBusinessRecord> KEY_T_ABILITY_BUSINESS_PRIMARY = UniqueKeys0.KEY_T_ABILITY_BUSINESS_PRIMARY;
    public static final UniqueKey<AbilityBusinessRecord> KEY_T_ABILITY_BUSINESS_UNIQ_ABILITY_ID_BUSINESS_ID = UniqueKeys0.KEY_T_ABILITY_BUSINESS_UNIQ_ABILITY_ID_BUSINESS_ID;
    public static final UniqueKey<CourseAbilityRecord> KEY_T_COURSE_ABILITY_PRIMARY = UniqueKeys0.KEY_T_COURSE_ABILITY_PRIMARY;
    public static final UniqueKey<AiSynchronousRecord> KEY_T_AI_SYNCHRONOUS_PRIMARY = UniqueKeys0.KEY_T_AI_SYNCHRONOUS_PRIMARY;
    public static final UniqueKey<CourseOnlineLogRecord> KEY_T_COURSE_ONLINE_LOG_PRIMARY = UniqueKeys0.KEY_T_COURSE_ONLINE_LOG_PRIMARY;
    public static final UniqueKey<DigitalMentorCallbackRecord> KEY_T_DIGITAL_MENTOR_CALLBACK_PRIMARY = UniqueKeys0.KEY_T_DIGITAL_MENTOR_CALLBACK_PRIMARY;


    public static final UniqueKey<PartySchoolNewsRecord> KEY_T_PARTY_SCHOOL_NEWS_PRIMARY = UniqueKeys0.KEY_T_PARTY_SCHOOL_NEWS_PRIMARY;

    public static final UniqueKey<CourseFeedbackRecord> KEY_T_COURSE_FEEDBACK_PRIMARY = UniqueKeys0.KEY_T_COURSE_FEEDBACK_PRIMARY;
    public static final UniqueKey<CourseKnowledgeRecord> KEY_T_COURSE_KNOWLEDGE_PRIMARY = UniqueKeys0.KEY_T_COURSE_KNOWLEDGE_PRIMARY;
    public static final UniqueKey<CourseQuestionRecommendRecord> KEY_T_COURSE_QUESTION_RECOMMEND_PRIMARY = UniqueKeys0.KEY_T_COURSE_QUESTION_RECOMMEND_PRIMARY;
    public static final UniqueKey<ChatTimeRecordRecord> KEY_T_CHAT_TIME_RECORD_PRIMARY = UniqueKeys0.KEY_T_CHAT_TIME_RECORD_PRIMARY;
    public static final UniqueKey<SubAuthenticatedTmpRecord> KEY_T_SUB_AUTHENTICATED_TMP_PRIMARY = UniqueKeys0.KEY_T_SUB_AUTHENTICATED_TMP_PRIMARY;
    public static final UniqueKey<DigitalIntelligenceMentorRecord> KEY_T_DIGITAL_INTELLIGENCE_MENTOR_PRIMARY = UniqueKeys0.KEY_T_DIGITAL_INTELLIGENCE_MENTOR_PRIMARY;
    // -------------------------------------------------------------------------
    // FOREIGN KEY definitions
    // -------------------------------------------------------------------------


    // -------------------------------------------------------------------------
    // [#1459] distribute members to avoid static initialisers > 64kb
    // -------------------------------------------------------------------------

    private static class Identities0 extends AbstractKeys {
        public static Identity<DbaAnalyzeTableIndexRecord, Long> IDENTITY_DBA_ANALYZE_TABLE_INDEX = createIdentity(DbaAnalyzeTableIndex.DBA_ANALYZE_TABLE_INDEX, DbaAnalyzeTableIndex.DBA_ANALYZE_TABLE_INDEX.TID);
        public static Identity<CourseOnlineLogRecord, Long> IDENTITY_COURSE_ONLINE_LOG = createIdentity(CourseOnlineLog.COURSE_ONLINE_LOG, CourseOnlineLog.COURSE_ONLINE_LOG.ID);

    }

    private static class UniqueKeys0 extends AbstractKeys {
        public static final UniqueKey<MiguConfigRecord> KEY_T_MIGU_CONFIG_PRIMARY = createUniqueKey(MiguConfig.MIGU_CONFIG, "KEY_t_migu_config_PRIMARY", MiguConfig.MIGU_CONFIG.ID);
        public static final UniqueKey<ActivityBannerRecord> KEY_T_ACTIVITY_BANNER_PRIMARY = createUniqueKey(ActivityBanner.ACTIVITY_BANNER, "KEY_t_activity_banner_PRIMARY", ActivityBanner.ACTIVITY_BANNER.ID);
        public static final UniqueKey<ActivityChbnRecord> KEY_T_ACTIVITY_CHBN_PRIMARY = createUniqueKey(ActivityChbn.ACTIVITY_CHBN, "KEY_t_activity_chbn_PRIMARY", ActivityChbn.ACTIVITY_CHBN.ID);
        public static final UniqueKey<AnnualBill_2019Record> KEY_T_ANNUAL_BILL_2019_PRIMARY = createUniqueKey(AnnualBill_2019.ANNUAL_BILL_2019, "KEY_t_annual_bill_2019_PRIMARY", AnnualBill_2019.ANNUAL_BILL_2019.ID);
        public static final UniqueKey<AnnualBill_2021Record> KEY_T_ANNUAL_BILL_2021_PRIMARY = createUniqueKey(AnnualBill_2021.ANNUAL_BILL_2021, "KEY_t_annual_bill_2021_PRIMARY", AnnualBill_2021.ANNUAL_BILL_2021.ID);
        public static final UniqueKey<AnnualBillAskbarcommentRecord> KEY_T_ANNUAL_BILL_ASKBARCOMMENT_PRIMARY = createUniqueKey(AnnualBillAskbarcomment.ANNUAL_BILL_ASKBARCOMMENT, "KEY_t_annual_bill_askbarcomment_PRIMARY", AnnualBillAskbarcomment.ANNUAL_BILL_ASKBARCOMMENT.ID);
        public static final UniqueKey<AnnualBillCoursecommentRecord> KEY_T_ANNUAL_BILL_COURSECOMMENT_PRIMARY = createUniqueKey(AnnualBillCoursecomment.ANNUAL_BILL_COURSECOMMENT, "KEY_t_annual_bill_coursecomment_PRIMARY", AnnualBillCoursecomment.ANNUAL_BILL_COURSECOMMENT.ID);
        public static final UniqueKey<AnnualBillExamRecord> KEY_T_ANNUAL_BILL_EXAM_PRIMARY = createUniqueKey(AnnualBillExam.ANNUAL_BILL_EXAM, "KEY_t_annual_bill_exam_PRIMARY", AnnualBillExam.ANNUAL_BILL_EXAM.ID);
        public static final UniqueKey<AnnualBillStudydayRecord> KEY_T_ANNUAL_BILL_STUDYDAY_PRIMARY = createUniqueKey(AnnualBillStudyday.ANNUAL_BILL_STUDYDAY, "KEY_t_annual_bill_studyday_PRIMARY", AnnualBillStudyday.ANNUAL_BILL_STUDYDAY.ID);
        public static final UniqueKey<AudienceItemRecord> KEY_T_AUDIENCE_ITEM_PRIMARY = createUniqueKey(AudienceItem.AUDIENCE_ITEM, "KEY_t_audience_item_PRIMARY", AudienceItem.AUDIENCE_ITEM.ID);
        public static final UniqueKey<AudienceMemberRecord> KEY_T_AUDIENCE_MEMBER_PRIMARY = createUniqueKey(AudienceMember.AUDIENCE_MEMBER, "KEY_t_audience_member_PRIMARY", AudienceMember.AUDIENCE_MEMBER.ID);
        public static final UniqueKey<AudienceObjectRecord> KEY_T_AUDIENCE_OBJECT_PRIMARY = createUniqueKey(AudienceObject.AUDIENCE_OBJECT, "KEY_t_audience_object_PRIMARY", AudienceObject.AUDIENCE_OBJECT.ID);
        public static final UniqueKey<BillConfigRecord> KEY_T_BILL_CONFIG_PRIMARY = createUniqueKey(BillConfig.BILL_CONFIG, "KEY_t_bill_config_PRIMARY", BillConfig.BILL_CONFIG.ID);
        public static final UniqueKey<BillConfigLostRecord> KEY_T_BILL_CONFIG_LOST_PRIMARY = createUniqueKey(BillConfigLost.BILL_CONFIG_LOST, "KEY_t_bill_config_lost_PRIMARY", BillConfigLost.BILL_CONFIG_LOST.ID);
        public static final UniqueKey<BusinessCertificateRecord> KEY_T_BUSINESS_CERTIFICATE_PRIMARY = createUniqueKey(BusinessCertificate.BUSINESS_CERTIFICATE, "KEY_t_business_certificate_PRIMARY", BusinessCertificate.BUSINESS_CERTIFICATE.ID);
        public static final UniqueKey<BusinessCertificateRecord> KEY_T_BUSINESS_CERTIFICATE_UNIQUE_T_BUSINESS_CERTIFICATE_BUSINESSID = createUniqueKey(BusinessCertificate.BUSINESS_CERTIFICATE, "KEY_t_business_certificate_unique_t_business_certificate_businessId", BusinessCertificate.BUSINESS_CERTIFICATE.BUSINESS_ID);
        public static final UniqueKey<BusinessTopicRecord> KEY_T_BUSINESS_TOPIC_PRIMARY = createUniqueKey(BusinessTopic.BUSINESS_TOPIC, "KEY_t_business_topic_PRIMARY", BusinessTopic.BUSINESS_TOPIC.ID);
        public static final UniqueKey<CertificateRecordRecord> KEY_T_CERTIFICATE_RECORD_PRIMARY = createUniqueKey(CertificateRecord.CERTIFICATE_RECORD, "KEY_t_certificate_record_PRIMARY", CertificateRecord.CERTIFICATE_RECORD.ID);
        public static final UniqueKey<CertificateRecordRecord> KEY_T_CERTIFICATE_RECORD_UNIQUE_CERTIFICATE_RECORD_MEMBERID_BUSINESS_ID = createUniqueKey(CertificateRecord.CERTIFICATE_RECORD, "KEY_t_certificate_record_unique_certificate_record_memberId_business_id", CertificateRecord.CERTIFICATE_RECORD.MEMBER_ID, CertificateRecord.CERTIFICATE_RECORD.BUSINESS_ID);
        public static final UniqueKey<CertificateRecordChbnRecord> KEY_T_CERTIFICATE_RECORD_CHBN_PRIMARY = createUniqueKey(CertificateRecordChbn.CERTIFICATE_RECORD_CHBN, "KEY_t_certificate_record_chbn_PRIMARY", CertificateRecordChbn.CERTIFICATE_RECORD_CHBN.ID);
        public static final UniqueKey<CertificateRecordChbnRecord> KEY_T_CERTIFICATE_RECORD_CHBN_UNIQUE_CERTIFICATE_RECORD_MEMBERID_BUSINESS_ID = createUniqueKey(CertificateRecordChbn.CERTIFICATE_RECORD_CHBN, "KEY_t_certificate_record_chbn_unique_certificate_record_memberId_business_id", CertificateRecordChbn.CERTIFICATE_RECORD_CHBN.MEMBER_ID, CertificateRecordChbn.CERTIFICATE_RECORD_CHBN.BUSINESS_ID);
        public static final UniqueKey<CompeteCourseAttachmentRecord> KEY_T_COMPETE_COURSE_ATTACHMENT_PRIMARY = createUniqueKey(CompeteCourseAttachment.COMPETE_COURSE_ATTACHMENT, "KEY_t_compete_course_attachment_PRIMARY", CompeteCourseAttachment.COMPETE_COURSE_ATTACHMENT.ID);
        public static final UniqueKey<CompeteCourseChapterSectionRecord> KEY_T_COMPETE_COURSE_CHAPTER_SECTION_PRIMARY = createUniqueKey(CompeteCourseChapterSection.COMPETE_COURSE_CHAPTER_SECTION, "KEY_t_compete_course_chapter_section_PRIMARY", CompeteCourseChapterSection.COMPETE_COURSE_CHAPTER_SECTION.ID);
        public static final UniqueKey<CompeteCourseInfoRecord> KEY_T_COMPETE_COURSE_INFO_PRIMARY = createUniqueKey(CompeteCourseInfo.COMPETE_COURSE_INFO, "KEY_t_compete_course_info_PRIMARY", CompeteCourseInfo.COMPETE_COURSE_INFO.ID);
        public static final UniqueKey<CompeteCourseVoteRecord> KEY_T_COMPETE_COURSE_VOTE_PRIMARY = createUniqueKey(CompeteCourseVote.COMPETE_COURSE_VOTE, "KEY_t_compete_course_vote_PRIMARY", CompeteCourseVote.COMPETE_COURSE_VOTE.ID);
        public static final UniqueKey<CompeteCourseVoteRecord> KEY_T_COMPETE_COURSE_VOTE_VOTE_COURSE = createUniqueKey(CompeteCourseVote.COMPETE_COURSE_VOTE, "KEY_t_compete_course_vote_vote_course", CompeteCourseVote.COMPETE_COURSE_VOTE.COURSE_ID, CompeteCourseVote.COMPETE_COURSE_VOTE.VOTE_MEMBER_ID);
        public static final UniqueKey<CompeteLecturerCompanyRecord> KEY_T_COMPETE_LECTURER_COMPANY_PRIMARY = createUniqueKey(CompeteLecturerCompany.COMPETE_LECTURER_COMPANY, "KEY_t_compete_lecturer_company_PRIMARY", CompeteLecturerCompany.COMPETE_LECTURER_COMPANY.ID);
        public static final UniqueKey<CourseAttachmentRecord> KEY_T_COURSE_ATTACHMENT_PRIMARY = createUniqueKey(CourseAttachment.COURSE_ATTACHMENT, "KEY_t_course_attachment_PRIMARY", CourseAttachment.COURSE_ATTACHMENT.ID);
        public static final UniqueKey<CourseCategoryRecord> KEY_T_COURSE_CATEGORY_PRIMARY = createUniqueKey(CourseCategory.COURSE_CATEGORY, "KEY_t_course_category_PRIMARY", CourseCategory.COURSE_CATEGORY.ID);
        public static final UniqueKey<CourseCertificateRecordRecord> KEY_T_COURSE_CERTIFICATE_RECORD_PRIMARY = createUniqueKey(CourseCertificateRecord.COURSE_CERTIFICATE_RECORD, "KEY_t_course_certificate_record_PRIMARY", CourseCertificateRecord.COURSE_CERTIFICATE_RECORD.ID);
        public static final UniqueKey<CourseCertificateRecordRecord> KEY_T_COURSE_CERTIFICATE_RECORD_IDX_COURSE_CERTIFICATE_RECORD_UNIQUEKEY_MEMBERID = createUniqueKey(CourseCertificateRecord.COURSE_CERTIFICATE_RECORD, "KEY_t_course_certificate_record_idx_course_certificate_record_uniqueKey_memberId", CourseCertificateRecord.COURSE_CERTIFICATE_RECORD.MEMBER_ID);
        public static final UniqueKey<CourseChapterRecord> KEY_T_COURSE_CHAPTER_PRIMARY = createUniqueKey(CourseChapter.COURSE_CHAPTER, "KEY_t_course_chapter_PRIMARY", CourseChapter.COURSE_CHAPTER.ID);
        public static final UniqueKey<CourseChapterQuestionnaireRecord> KEY_T_COURSE_CHAPTER_QUESTIONNAIRE_PRIMARY = createUniqueKey(CourseChapterQuestionnaire.COURSE_CHAPTER_QUESTIONNAIRE, "KEY_t_course_chapter_questionnaire_PRIMARY", CourseChapterQuestionnaire.COURSE_CHAPTER_QUESTIONNAIRE.ID);
        public static final UniqueKey<CourseChapterSectionRecord> KEY_T_COURSE_CHAPTER_SECTION_PRIMARY = createUniqueKey(CourseChapterSection.COURSE_CHAPTER_SECTION, "KEY_t_course_chapter_section_PRIMARY", CourseChapterSection.COURSE_CHAPTER_SECTION.ID);
        public static final UniqueKey<CourseCurrencyRecord> KEY_T_COURSE_CURRENCY_PRIMARY = createUniqueKey(CourseCurrency.COURSE_CURRENCY, "KEY_t_course_currency_PRIMARY", CourseCurrency.COURSE_CURRENCY.ID);
        public static final UniqueKey<CourseExceptionRecord> KEY_T_COURSE_EXCEPTION_PRIMARY = createUniqueKey(CourseException.COURSE_EXCEPTION, "KEY_t_course_exception_PRIMARY", CourseException.COURSE_EXCEPTION.ID);
        public static final UniqueKey<CourseInfoRecord> KEY_T_COURSE_INFO_PRIMARY = createUniqueKey(CourseInfo.COURSE_INFO, "KEY_t_course_info_PRIMARY", CourseInfo.COURSE_INFO.ID);
        public static final UniqueKey<CourseInfoCategoryRecord> KEY_T_COURSE_INFO_CATEGORY_PRIMARY = createUniqueKey(CourseInfoCategory.COURSE_INFO_CATEGORY, "KEY_t_course_info_category_PRIMARY", CourseInfoCategory.COURSE_INFO_CATEGORY.ID);
        public static final UniqueKey<CourseInformRecord> KEY_T_COURSE_INFORM_PRIMARY = createUniqueKey(CourseInform.COURSE_INFORM, "KEY_t_course_inform_PRIMARY", CourseInform.COURSE_INFORM.ID);
        public static final UniqueKey<CourseNoteRecord> KEY_T_COURSE_NOTE_PRIMARY = createUniqueKey(CourseNote.COURSE_NOTE, "KEY_t_course_note_PRIMARY", CourseNote.COURSE_NOTE.ID);
        public static final UniqueKey<CoursePhotoRecord> KEY_T_COURSE_PHOTO_PRIMARY = createUniqueKey(CoursePhoto.COURSE_PHOTO, "KEY_t_course_photo_PRIMARY", CoursePhoto.COURSE_PHOTO.ID);
        public static final UniqueKey<CourseQuestionnaireRecordRecord> KEY_T_COURSE_QUESTIONNAIRE_RECORD_PRIMARY = createUniqueKey(CourseQuestionnaireRecord.COURSE_QUESTIONNAIRE_RECORD, "KEY_t_course_questionnaire_record_PRIMARY", CourseQuestionnaireRecord.COURSE_QUESTIONNAIRE_RECORD.ID);
        public static final UniqueKey<CourseRecommendRecord> KEY_T_COURSE_RECOMMEND_PRIMARY = createUniqueKey(CourseRecommend.COURSE_RECOMMEND, "KEY_t_course_recommend_PRIMARY", CourseRecommend.COURSE_RECOMMEND.ID);
        public static final UniqueKey<CourseRecordRecord> KEY_T_COURSE_RECORD_PRIMARY = createUniqueKey(CourseRecord.COURSE_RECORD, "KEY_t_course_record_PRIMARY", CourseRecord.COURSE_RECORD.ID);
        public static final UniqueKey<CourseRegisterRecord> KEY_T_COURSE_REGISTER_PRIMARY = createUniqueKey(CourseRegister.COURSE_REGISTER, "KEY_t_course_register_PRIMARY", CourseRegister.COURSE_REGISTER.ID);
        public static final UniqueKey<CourseScoreRecord> KEY_T_COURSE_SCORE_PRIMARY = createUniqueKey(CourseScore.COURSE_SCORE, "KEY_t_course_score_PRIMARY", CourseScore.COURSE_SCORE.ID);
        public static final UniqueKey<CourseSectionProgressAttachmentRecord> KEY_T_COURSE_SECTION_PROGRESS_ATTACHMENT_PRIMARY = createUniqueKey(CourseSectionProgressAttachment.COURSE_SECTION_PROGRESS_ATTACHMENT, "KEY_t_course_section_progress_attachment_PRIMARY", CourseSectionProgressAttachment.COURSE_SECTION_PROGRESS_ATTACHMENT.ID);
        public static final UniqueKey<CourseSectionScormRecord> KEY_T_COURSE_SECTION_SCORM_PRIMARY = createUniqueKey(CourseSectionScorm.COURSE_SECTION_SCORM, "KEY_t_course_section_scorm_PRIMARY", CourseSectionScorm.COURSE_SECTION_SCORM.ID);
        public static final UniqueKey<CourseSectionScormProgressRecord> KEY_T_COURSE_SECTION_SCORM_PROGRESS_PRIMARY = createUniqueKey(CourseSectionScormProgress.COURSE_SECTION_SCORM_PROGRESS, "KEY_t_course_section_scorm_progress_PRIMARY", CourseSectionScormProgress.COURSE_SECTION_SCORM_PROGRESS.ID);
        public static final UniqueKey<CourseSectionStudyLogRecord> KEY_T_COURSE_SECTION_STUDY_LOG_PRIMARY = createUniqueKey(CourseSectionStudyLog.COURSE_SECTION_STUDY_LOG, "KEY_t_course_section_study_log_PRIMARY", CourseSectionStudyLog.COURSE_SECTION_STUDY_LOG.ID);
        public static final UniqueKey<CourseSectionStudyLogAhRecord> KEY_T_COURSE_SECTION_STUDY_LOG_AH_PRIMARY = createUniqueKey(CourseSectionStudyLogAh.COURSE_SECTION_STUDY_LOG_AH, "KEY_t_course_section_study_log_ah_PRIMARY", CourseSectionStudyLogAh.COURSE_SECTION_STUDY_LOG_AH.ID);
        public static final UniqueKey<CourseSectionStudyLogAhDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_AH_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogAhDay.COURSE_SECTION_STUDY_LOG_AH_DAY, "KEY_t_course_section_study_log_ah_day_PRIMARY", CourseSectionStudyLogAhDay.COURSE_SECTION_STUDY_LOG_AH_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogBjRecord> KEY_T_COURSE_SECTION_STUDY_LOG_BJ_PRIMARY = createUniqueKey(CourseSectionStudyLogBj.COURSE_SECTION_STUDY_LOG_BJ, "KEY_t_course_section_study_log_bj_PRIMARY", CourseSectionStudyLogBj.COURSE_SECTION_STUDY_LOG_BJ.ID);
        public static final UniqueKey<CourseSectionStudyLogBjDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_BJ_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogBjDay.COURSE_SECTION_STUDY_LOG_BJ_DAY, "KEY_t_course_section_study_log_bj_day_PRIMARY", CourseSectionStudyLogBjDay.COURSE_SECTION_STUDY_LOG_BJ_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogCmRecord> KEY_T_COURSE_SECTION_STUDY_LOG_CM_PRIMARY = createUniqueKey(CourseSectionStudyLogCm.COURSE_SECTION_STUDY_LOG_CM, "KEY_t_course_section_study_log_cm_PRIMARY", CourseSectionStudyLogCm.COURSE_SECTION_STUDY_LOG_CM.ID);
        public static final UniqueKey<CourseSectionStudyLogCmDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_CM_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogCmDay.COURSE_SECTION_STUDY_LOG_CM_DAY, "KEY_t_course_section_study_log_cm_day_PRIMARY", CourseSectionStudyLogCmDay.COURSE_SECTION_STUDY_LOG_CM_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogCqRecord> KEY_T_COURSE_SECTION_STUDY_LOG_CQ_PRIMARY = createUniqueKey(CourseSectionStudyLogCq.COURSE_SECTION_STUDY_LOG_CQ, "KEY_t_course_section_study_log_cq_PRIMARY", CourseSectionStudyLogCq.COURSE_SECTION_STUDY_LOG_CQ.ID);
        public static final UniqueKey<CourseSectionStudyLogCqDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_CQ_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogCqDay.COURSE_SECTION_STUDY_LOG_CQ_DAY, "KEY_t_course_section_study_log_cq_day_PRIMARY", CourseSectionStudyLogCqDay.COURSE_SECTION_STUDY_LOG_CQ_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogEbRecord> KEY_T_COURSE_SECTION_STUDY_LOG_EB_PRIMARY = createUniqueKey(CourseSectionStudyLogEb.COURSE_SECTION_STUDY_LOG_EB, "KEY_t_course_section_study_log_eb_PRIMARY", CourseSectionStudyLogEb.COURSE_SECTION_STUDY_LOG_EB.ID);
        public static final UniqueKey<CourseSectionStudyLogEbDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_EB_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogEbDay.COURSE_SECTION_STUDY_LOG_EB_DAY, "KEY_t_course_section_study_log_eb_day_PRIMARY", CourseSectionStudyLogEbDay.COURSE_SECTION_STUDY_LOG_EB_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogFjRecord> KEY_T_COURSE_SECTION_STUDY_LOG_FJ_PRIMARY = createUniqueKey(CourseSectionStudyLogFj.COURSE_SECTION_STUDY_LOG_FJ, "KEY_t_course_section_study_log_fj_PRIMARY", CourseSectionStudyLogFj.COURSE_SECTION_STUDY_LOG_FJ.ID);
        public static final UniqueKey<CourseSectionStudyLogFjDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_FJ_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogFjDay.COURSE_SECTION_STUDY_LOG_FJ_DAY, "KEY_t_course_section_study_log_fj_day_PRIMARY", CourseSectionStudyLogFjDay.COURSE_SECTION_STUDY_LOG_FJ_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogGdRecord> KEY_T_COURSE_SECTION_STUDY_LOG_GD_PRIMARY = createUniqueKey(CourseSectionStudyLogGd.COURSE_SECTION_STUDY_LOG_GD, "KEY_t_course_section_study_log_gd_PRIMARY", CourseSectionStudyLogGd.COURSE_SECTION_STUDY_LOG_GD.ID);
        public static final UniqueKey<CourseSectionStudyLogGdDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_GD_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogGdDay.COURSE_SECTION_STUDY_LOG_GD_DAY, "KEY_t_course_section_study_log_gd_day_PRIMARY", CourseSectionStudyLogGdDay.COURSE_SECTION_STUDY_LOG_GD_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogGsRecord> KEY_T_COURSE_SECTION_STUDY_LOG_GS_PRIMARY = createUniqueKey(CourseSectionStudyLogGs.COURSE_SECTION_STUDY_LOG_GS, "KEY_t_course_section_study_log_gs_PRIMARY", CourseSectionStudyLogGs.COURSE_SECTION_STUDY_LOG_GS.ID);
        public static final UniqueKey<CourseSectionStudyLogGsDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_GS_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogGsDay.COURSE_SECTION_STUDY_LOG_GS_DAY, "KEY_t_course_section_study_log_gs_day_PRIMARY", CourseSectionStudyLogGsDay.COURSE_SECTION_STUDY_LOG_GS_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogGxRecord> KEY_T_COURSE_SECTION_STUDY_LOG_GX_PRIMARY = createUniqueKey(CourseSectionStudyLogGx.COURSE_SECTION_STUDY_LOG_GX, "KEY_t_course_section_study_log_gx_PRIMARY", CourseSectionStudyLogGx.COURSE_SECTION_STUDY_LOG_GX.ID);
        public static final UniqueKey<CourseSectionStudyLogGxDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_GX_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogGxDay.COURSE_SECTION_STUDY_LOG_GX_DAY, "KEY_t_course_section_study_log_gx_day_PRIMARY", CourseSectionStudyLogGxDay.COURSE_SECTION_STUDY_LOG_GX_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogGzRecord> KEY_T_COURSE_SECTION_STUDY_LOG_GZ_PRIMARY = createUniqueKey(CourseSectionStudyLogGz.COURSE_SECTION_STUDY_LOG_GZ, "KEY_t_course_section_study_log_gz_PRIMARY", CourseSectionStudyLogGz.COURSE_SECTION_STUDY_LOG_GZ.ID);
        public static final UniqueKey<CourseSectionStudyLogGzDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_GZ_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogGzDay.COURSE_SECTION_STUDY_LOG_GZ_DAY, "KEY_t_course_section_study_log_gz_day_PRIMARY", CourseSectionStudyLogGzDay.COURSE_SECTION_STUDY_LOG_GZ_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogHbRecord> KEY_T_COURSE_SECTION_STUDY_LOG_HB_PRIMARY = createUniqueKey(CourseSectionStudyLogHb.COURSE_SECTION_STUDY_LOG_HB, "KEY_t_course_section_study_log_hb_PRIMARY", CourseSectionStudyLogHb.COURSE_SECTION_STUDY_LOG_HB.ID);
        public static final UniqueKey<CourseSectionStudyLogHbDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_HB_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogHbDay.COURSE_SECTION_STUDY_LOG_HB_DAY, "KEY_t_course_section_study_log_hb_day_PRIMARY", CourseSectionStudyLogHbDay.COURSE_SECTION_STUDY_LOG_HB_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogHlRecord> KEY_T_COURSE_SECTION_STUDY_LOG_HL_PRIMARY = createUniqueKey(CourseSectionStudyLogHl.COURSE_SECTION_STUDY_LOG_HL, "KEY_t_course_section_study_log_hl_PRIMARY", CourseSectionStudyLogHl.COURSE_SECTION_STUDY_LOG_HL.ID);
        public static final UniqueKey<CourseSectionStudyLogHlDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_HL_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogHlDay.COURSE_SECTION_STUDY_LOG_HL_DAY, "KEY_t_course_section_study_log_hl_day_PRIMARY", CourseSectionStudyLogHlDay.COURSE_SECTION_STUDY_LOG_HL_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogHnRecord> KEY_T_COURSE_SECTION_STUDY_LOG_HN_PRIMARY = createUniqueKey(CourseSectionStudyLogHn.COURSE_SECTION_STUDY_LOG_HN, "KEY_t_course_section_study_log_hn_PRIMARY", CourseSectionStudyLogHn.COURSE_SECTION_STUDY_LOG_HN.ID);
        public static final UniqueKey<CourseSectionStudyLogHnDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_HN_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogHnDay.COURSE_SECTION_STUDY_LOG_HN_DAY, "KEY_t_course_section_study_log_hn_day_PRIMARY", CourseSectionStudyLogHnDay.COURSE_SECTION_STUDY_LOG_HN_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogJlRecord> KEY_T_COURSE_SECTION_STUDY_LOG_JL_PRIMARY = createUniqueKey(CourseSectionStudyLogJl.COURSE_SECTION_STUDY_LOG_JL, "KEY_t_course_section_study_log_jl_PRIMARY", CourseSectionStudyLogJl.COURSE_SECTION_STUDY_LOG_JL.ID);
        public static final UniqueKey<CourseSectionStudyLogJlDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_JL_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogJlDay.COURSE_SECTION_STUDY_LOG_JL_DAY, "KEY_t_course_section_study_log_jl_day_PRIMARY", CourseSectionStudyLogJlDay.COURSE_SECTION_STUDY_LOG_JL_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogJsRecord> KEY_T_COURSE_SECTION_STUDY_LOG_JS_PRIMARY = createUniqueKey(CourseSectionStudyLogJs.COURSE_SECTION_STUDY_LOG_JS, "KEY_t_course_section_study_log_js_PRIMARY", CourseSectionStudyLogJs.COURSE_SECTION_STUDY_LOG_JS.ID);
        public static final UniqueKey<CourseSectionStudyLogJsDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_JS_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogJsDay.COURSE_SECTION_STUDY_LOG_JS_DAY, "KEY_t_course_section_study_log_js_day_PRIMARY", CourseSectionStudyLogJsDay.COURSE_SECTION_STUDY_LOG_JS_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogJxRecord> KEY_T_COURSE_SECTION_STUDY_LOG_JX_PRIMARY = createUniqueKey(CourseSectionStudyLogJx.COURSE_SECTION_STUDY_LOG_JX, "KEY_t_course_section_study_log_jx_PRIMARY", CourseSectionStudyLogJx.COURSE_SECTION_STUDY_LOG_JX.ID);
        public static final UniqueKey<CourseSectionStudyLogJxDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_JX_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogJxDay.COURSE_SECTION_STUDY_LOG_JX_DAY, "KEY_t_course_section_study_log_jx_day_PRIMARY", CourseSectionStudyLogJxDay.COURSE_SECTION_STUDY_LOG_JX_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogLnRecord> KEY_T_COURSE_SECTION_STUDY_LOG_LN_PRIMARY = createUniqueKey(CourseSectionStudyLogLn.COURSE_SECTION_STUDY_LOG_LN, "KEY_t_course_section_study_log_ln_PRIMARY", CourseSectionStudyLogLn.COURSE_SECTION_STUDY_LOG_LN.ID);
        public static final UniqueKey<CourseSectionStudyLogLnDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_LN_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogLnDay.COURSE_SECTION_STUDY_LOG_LN_DAY, "KEY_t_course_section_study_log_ln_day_PRIMARY", CourseSectionStudyLogLnDay.COURSE_SECTION_STUDY_LOG_LN_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogNmRecord> KEY_T_COURSE_SECTION_STUDY_LOG_NM_PRIMARY = createUniqueKey(CourseSectionStudyLogNm.COURSE_SECTION_STUDY_LOG_NM, "KEY_t_course_section_study_log_nm_PRIMARY", CourseSectionStudyLogNm.COURSE_SECTION_STUDY_LOG_NM.ID);
        public static final UniqueKey<CourseSectionStudyLogNmDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_NM_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogNmDay.COURSE_SECTION_STUDY_LOG_NM_DAY, "KEY_t_course_section_study_log_nm_day_PRIMARY", CourseSectionStudyLogNmDay.COURSE_SECTION_STUDY_LOG_NM_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogNxRecord> KEY_T_COURSE_SECTION_STUDY_LOG_NX_PRIMARY = createUniqueKey(CourseSectionStudyLogNx.COURSE_SECTION_STUDY_LOG_NX, "KEY_t_course_section_study_log_nx_PRIMARY", CourseSectionStudyLogNx.COURSE_SECTION_STUDY_LOG_NX.ID);
        public static final UniqueKey<CourseSectionStudyLogNxDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_NX_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogNxDay.COURSE_SECTION_STUDY_LOG_NX_DAY, "KEY_t_course_section_study_log_nx_day_PRIMARY", CourseSectionStudyLogNxDay.COURSE_SECTION_STUDY_LOG_NX_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogOtherRecord> KEY_T_COURSE_SECTION_STUDY_LOG_OTHER_PRIMARY = createUniqueKey(CourseSectionStudyLogOther.COURSE_SECTION_STUDY_LOG_OTHER, "KEY_t_course_section_study_log_other_PRIMARY", CourseSectionStudyLogOther.COURSE_SECTION_STUDY_LOG_OTHER.ID);
        public static final UniqueKey<CourseSectionStudyLogOtherDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_OTHER_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogOtherDay.COURSE_SECTION_STUDY_LOG_OTHER_DAY, "KEY_t_course_section_study_log_other_day_PRIMARY", CourseSectionStudyLogOtherDay.COURSE_SECTION_STUDY_LOG_OTHER_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogQhRecord> KEY_T_COURSE_SECTION_STUDY_LOG_QH_PRIMARY = createUniqueKey(CourseSectionStudyLogQh.COURSE_SECTION_STUDY_LOG_QH, "KEY_t_course_section_study_log_qh_PRIMARY", CourseSectionStudyLogQh.COURSE_SECTION_STUDY_LOG_QH.ID);
        public static final UniqueKey<CourseSectionStudyLogQhDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_QH_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogQhDay.COURSE_SECTION_STUDY_LOG_QH_DAY, "KEY_t_course_section_study_log_qh_day_PRIMARY", CourseSectionStudyLogQhDay.COURSE_SECTION_STUDY_LOG_QH_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogQoRecord> KEY_T_COURSE_SECTION_STUDY_LOG_QO_PRIMARY = createUniqueKey(CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO, "KEY_t_course_section_study_log_qo_PRIMARY", CourseSectionStudyLogQo.COURSE_SECTION_STUDY_LOG_QO.ID);
        public static final UniqueKey<CourseSectionStudyLogQoDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_QO_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogQoDay.COURSE_SECTION_STUDY_LOG_QO_DAY, "KEY_t_course_section_study_log_qo_day_PRIMARY", CourseSectionStudyLogQoDay.COURSE_SECTION_STUDY_LOG_QO_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogScRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SC_PRIMARY = createUniqueKey(CourseSectionStudyLogSc.COURSE_SECTION_STUDY_LOG_SC, "KEY_t_course_section_study_log_sc_PRIMARY", CourseSectionStudyLogSc.COURSE_SECTION_STUDY_LOG_SC.ID);
        public static final UniqueKey<CourseSectionStudyLogScDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SC_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogScDay.COURSE_SECTION_STUDY_LOG_SC_DAY, "KEY_t_course_section_study_log_sc_day_PRIMARY", CourseSectionStudyLogScDay.COURSE_SECTION_STUDY_LOG_SC_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogSdRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SD_PRIMARY = createUniqueKey(CourseSectionStudyLogSd.COURSE_SECTION_STUDY_LOG_SD, "KEY_t_course_section_study_log_sd_PRIMARY", CourseSectionStudyLogSd.COURSE_SECTION_STUDY_LOG_SD.ID);
        public static final UniqueKey<CourseSectionStudyLogSdDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SD_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogSdDay.COURSE_SECTION_STUDY_LOG_SD_DAY, "KEY_t_course_section_study_log_sd_day_PRIMARY", CourseSectionStudyLogSdDay.COURSE_SECTION_STUDY_LOG_SD_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogShRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SH_PRIMARY = createUniqueKey(CourseSectionStudyLogSh.COURSE_SECTION_STUDY_LOG_SH, "KEY_t_course_section_study_log_sh_PRIMARY", CourseSectionStudyLogSh.COURSE_SECTION_STUDY_LOG_SH.ID);
        public static final UniqueKey<CourseSectionStudyLogShDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SH_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogShDay.COURSE_SECTION_STUDY_LOG_SH_DAY, "KEY_t_course_section_study_log_sh_day_PRIMARY", CourseSectionStudyLogShDay.COURSE_SECTION_STUDY_LOG_SH_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogSnRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SN_PRIMARY = createUniqueKey(CourseSectionStudyLogSn.COURSE_SECTION_STUDY_LOG_SN, "KEY_t_course_section_study_log_sn_PRIMARY", CourseSectionStudyLogSn.COURSE_SECTION_STUDY_LOG_SN.ID);
        public static final UniqueKey<CourseSectionStudyLogSnDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SN_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogSnDay.COURSE_SECTION_STUDY_LOG_SN_DAY, "KEY_t_course_section_study_log_sn_day_PRIMARY", CourseSectionStudyLogSnDay.COURSE_SECTION_STUDY_LOG_SN_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogSxRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SX_PRIMARY = createUniqueKey(CourseSectionStudyLogSx.COURSE_SECTION_STUDY_LOG_SX, "KEY_t_course_section_study_log_sx_PRIMARY", CourseSectionStudyLogSx.COURSE_SECTION_STUDY_LOG_SX.ID);
        public static final UniqueKey<CourseSectionStudyLogSxDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_SX_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY, "KEY_t_course_section_study_log_sx_day_PRIMARY", CourseSectionStudyLogSxDay.COURSE_SECTION_STUDY_LOG_SX_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogTjRecord> KEY_T_COURSE_SECTION_STUDY_LOG_TJ_PRIMARY = createUniqueKey(CourseSectionStudyLogTj.COURSE_SECTION_STUDY_LOG_TJ, "KEY_t_course_section_study_log_tj_PRIMARY", CourseSectionStudyLogTj.COURSE_SECTION_STUDY_LOG_TJ.ID);
        public static final UniqueKey<CourseSectionStudyLogTjDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_TJ_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogTjDay.COURSE_SECTION_STUDY_LOG_TJ_DAY, "KEY_t_course_section_study_log_tj_day_PRIMARY", CourseSectionStudyLogTjDay.COURSE_SECTION_STUDY_LOG_TJ_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogXjRecord> KEY_T_COURSE_SECTION_STUDY_LOG_XJ_PRIMARY = createUniqueKey(CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ, "KEY_t_course_section_study_log_xj_PRIMARY", CourseSectionStudyLogXj.COURSE_SECTION_STUDY_LOG_XJ.ID);
        public static final UniqueKey<CourseSectionStudyLogXjDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_XJ_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogXjDay.COURSE_SECTION_STUDY_LOG_XJ_DAY, "KEY_t_course_section_study_log_xj_day_PRIMARY", CourseSectionStudyLogXjDay.COURSE_SECTION_STUDY_LOG_XJ_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogXnRecord> KEY_T_COURSE_SECTION_STUDY_LOG_XN_PRIMARY = createUniqueKey(CourseSectionStudyLogXn.COURSE_SECTION_STUDY_LOG_XN, "KEY_t_course_section_study_log_xn_PRIMARY", CourseSectionStudyLogXn.COURSE_SECTION_STUDY_LOG_XN.ID);
        public static final UniqueKey<CourseSectionStudyLogXnDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_XN_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogXnDay.COURSE_SECTION_STUDY_LOG_XN_DAY, "KEY_t_course_section_study_log_xn_day_PRIMARY", CourseSectionStudyLogXnDay.COURSE_SECTION_STUDY_LOG_XN_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogXzRecord> KEY_T_COURSE_SECTION_STUDY_LOG_XZ_PRIMARY = createUniqueKey(CourseSectionStudyLogXz.COURSE_SECTION_STUDY_LOG_XZ, "KEY_t_course_section_study_log_xz_PRIMARY", CourseSectionStudyLogXz.COURSE_SECTION_STUDY_LOG_XZ.ID);
        public static final UniqueKey<CourseSectionStudyLogXzDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_XZ_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogXzDay.COURSE_SECTION_STUDY_LOG_XZ_DAY, "KEY_t_course_section_study_log_xz_day_PRIMARY", CourseSectionStudyLogXzDay.COURSE_SECTION_STUDY_LOG_XZ_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogYnRecord> KEY_T_COURSE_SECTION_STUDY_LOG_YN_PRIMARY = createUniqueKey(CourseSectionStudyLogYn.COURSE_SECTION_STUDY_LOG_YN, "KEY_t_course_section_study_log_yn_PRIMARY", CourseSectionStudyLogYn.COURSE_SECTION_STUDY_LOG_YN.ID);
        public static final UniqueKey<CourseSectionStudyLogYnDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_YN_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogYnDay.COURSE_SECTION_STUDY_LOG_YN_DAY, "KEY_t_course_section_study_log_yn_day_PRIMARY", CourseSectionStudyLogYnDay.COURSE_SECTION_STUDY_LOG_YN_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogZgttRecord> KEY_T_COURSE_SECTION_STUDY_LOG_ZGTT_PRIMARY = createUniqueKey(CourseSectionStudyLogZgtt.COURSE_SECTION_STUDY_LOG_ZGTT, "KEY_t_course_section_study_log_zgtt_PRIMARY", CourseSectionStudyLogZgtt.COURSE_SECTION_STUDY_LOG_ZGTT.ID);
        public static final UniqueKey<CourseSectionStudyLogZgttDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_ZGTT_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogZgttDay.COURSE_SECTION_STUDY_LOG_ZGTT_DAY, "KEY_t_course_section_study_log_zgtt_day_PRIMARY", CourseSectionStudyLogZgttDay.COURSE_SECTION_STUDY_LOG_ZGTT_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogZjRecord> KEY_T_COURSE_SECTION_STUDY_LOG_ZJ_PRIMARY = createUniqueKey(CourseSectionStudyLogZj.COURSE_SECTION_STUDY_LOG_ZJ, "KEY_t_course_section_study_log_zj_PRIMARY", CourseSectionStudyLogZj.COURSE_SECTION_STUDY_LOG_ZJ.ID);
        public static final UniqueKey<CourseSectionStudyLogZjDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_ZJ_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogZjDay.COURSE_SECTION_STUDY_LOG_ZJ_DAY, "KEY_t_course_section_study_log_zj_day_PRIMARY", CourseSectionStudyLogZjDay.COURSE_SECTION_STUDY_LOG_ZJ_DAY.ID);
        public static final UniqueKey<CourseSectionStudyLogZxRecord> KEY_T_COURSE_SECTION_STUDY_LOG_ZX_PRIMARY = createUniqueKey(CourseSectionStudyLogZx.COURSE_SECTION_STUDY_LOG_ZX, "KEY_t_course_section_study_log_zx_PRIMARY", CourseSectionStudyLogZx.COURSE_SECTION_STUDY_LOG_ZX.ID);
        public static final UniqueKey<CourseSectionStudyLogZxDayRecord> KEY_T_COURSE_SECTION_STUDY_LOG_ZX_DAY_PRIMARY = createUniqueKey(CourseSectionStudyLogZxDay.COURSE_SECTION_STUDY_LOG_ZX_DAY, "KEY_t_course_section_study_log_zx_day_PRIMARY", CourseSectionStudyLogZxDay.COURSE_SECTION_STUDY_LOG_ZX_DAY.ID);
        public static final UniqueKey<CourseSectionStudyProgressRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_PRIMARY = createUniqueKey(CourseSectionStudyProgress.COURSE_SECTION_STUDY_PROGRESS, "KEY_t_course_section_study_progress_PRIMARY", CourseSectionStudyProgress.COURSE_SECTION_STUDY_PROGRESS.ID);
        public static final UniqueKey<CourseSectionStudyProgressRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgress.COURSE_SECTION_STUDY_PROGRESS, "KEY_t_course_section_study_progress_unique_t_course_section_p_member_section", CourseSectionStudyProgress.COURSE_SECTION_STUDY_PROGRESS.SECTION_ID, CourseSectionStudyProgress.COURSE_SECTION_STUDY_PROGRESS.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressChbncRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_CHBNC_PRIMARY = createUniqueKey(CourseSectionStudyProgressChbnc.COURSE_SECTION_STUDY_PROGRESS_CHBNC, "KEY_t_course_section_study_progress_chbnc_PRIMARY", CourseSectionStudyProgressChbnc.COURSE_SECTION_STUDY_PROGRESS_CHBNC.ID);
        public static final UniqueKey<CourseSectionStudyProgressChbncRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_CHBNC_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressChbnc.COURSE_SECTION_STUDY_PROGRESS_CHBNC, "KEY_t_course_section_study_progress_chbnc_unique_t_course_section_p_member_section", CourseSectionStudyProgressChbnc.COURSE_SECTION_STUDY_PROGRESS_CHBNC.MEMBER_ID, CourseSectionStudyProgressChbnc.COURSE_SECTION_STUDY_PROGRESS_CHBNC.SECTION_ID);
        public static final UniqueKey<CourseSectionStudyProgressChbnsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_CHBNS_PRIMARY = createUniqueKey(CourseSectionStudyProgressChbns.COURSE_SECTION_STUDY_PROGRESS_CHBNS, "KEY_t_course_section_study_progress_chbns_PRIMARY", CourseSectionStudyProgressChbns.COURSE_SECTION_STUDY_PROGRESS_CHBNS.ID);
        public static final UniqueKey<CourseSectionStudyProgressChbnsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_CHBNS_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressChbns.COURSE_SECTION_STUDY_PROGRESS_CHBNS, "KEY_t_course_section_study_progress_chbns_unique_t_course_section_p_member_section", CourseSectionStudyProgressChbns.COURSE_SECTION_STUDY_PROGRESS_CHBNS.SECTION_ID, CourseSectionStudyProgressChbns.COURSE_SECTION_STUDY_PROGRESS_CHBNS.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressFfclcRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_PRIMARY = createUniqueKey(CourseSectionStudyProgressFfclc.COURSE_SECTION_STUDY_PROGRESS_FFCLC, "KEY_t_course_section_study_progress_ffclc_PRIMARY", CourseSectionStudyProgressFfclc.COURSE_SECTION_STUDY_PROGRESS_FFCLC.ID);
        public static final UniqueKey<CourseSectionStudyProgressFfclcRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_UNIQUE_T_CSSP_FFCLC_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressFfclc.COURSE_SECTION_STUDY_PROGRESS_FFCLC, "KEY_t_course_section_study_progress_ffclc_unique_t_cssp_ffclc_member_section", CourseSectionStudyProgressFfclc.COURSE_SECTION_STUDY_PROGRESS_FFCLC.MEMBER_ID, CourseSectionStudyProgressFfclc.COURSE_SECTION_STUDY_PROGRESS_FFCLC.SECTION_ID);
        public static final UniqueKey<CourseSectionStudyProgressFfclsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_PRIMARY = createUniqueKey(CourseSectionStudyProgressFfcls.COURSE_SECTION_STUDY_PROGRESS_FFCLS, "KEY_t_course_section_study_progress_ffcls_PRIMARY", CourseSectionStudyProgressFfcls.COURSE_SECTION_STUDY_PROGRESS_FFCLS.ID);
        public static final UniqueKey<CourseSectionStudyProgressFfclsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_UNIQUE_T_CSSP_FFCLS_SECTION_MEMBER = createUniqueKey(CourseSectionStudyProgressFfcls.COURSE_SECTION_STUDY_PROGRESS_FFCLS, "KEY_t_course_section_study_progress_ffcls_unique_t_cssp_ffcls_section_member", CourseSectionStudyProgressFfcls.COURSE_SECTION_STUDY_PROGRESS_FFCLS.SECTION_ID, CourseSectionStudyProgressFfcls.COURSE_SECTION_STUDY_PROGRESS_FFCLS.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressFfcls_2022Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022_PRIMARY = createUniqueKey(CourseSectionStudyProgressFfcls_2022.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022, "KEY_t_course_section_study_progress_ffcls_2022_PRIMARY", CourseSectionStudyProgressFfcls_2022.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022.ID);
        public static final UniqueKey<CourseSectionStudyProgressFfcls_2022Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022_UNIQUE_T_CSSP_FFCLS_2022_SECTION_MEMBER = createUniqueKey(CourseSectionStudyProgressFfcls_2022.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022, "KEY_t_course_section_study_progress_ffcls_2022_unique_t_cssp_ffcls_2022_section_member", CourseSectionStudyProgressFfcls_2022.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022.SECTION_ID, CourseSectionStudyProgressFfcls_2022.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressRtsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_RTS_PRIMARY = createUniqueKey(CourseSectionStudyProgressRts.COURSE_SECTION_STUDY_PROGRESS_RTS, "KEY_t_course_section_study_progress_rts_PRIMARY", CourseSectionStudyProgressRts.COURSE_SECTION_STUDY_PROGRESS_RTS.ID);
        public static final UniqueKey<CourseSectionStudyProgressRtsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_RTS_UNIQUE_T_CSSP_XDNS_SECTION_MEMBER = createUniqueKey(CourseSectionStudyProgressRts.COURSE_SECTION_STUDY_PROGRESS_RTS, "KEY_t_course_section_study_progress_rts_unique_t_cssp_xdns_section_member", CourseSectionStudyProgressRts.COURSE_SECTION_STUDY_PROGRESS_RTS.SECTION_ID, CourseSectionStudyProgressRts.COURSE_SECTION_STUDY_PROGRESS_RTS.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressXdncRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XDNC_PRIMARY = createUniqueKey(CourseSectionStudyProgressXdnc.COURSE_SECTION_STUDY_PROGRESS_XDNC, "KEY_t_course_section_study_progress_xdnc_PRIMARY", CourseSectionStudyProgressXdnc.COURSE_SECTION_STUDY_PROGRESS_XDNC.ID);
        public static final UniqueKey<CourseSectionStudyProgressXdncRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XDNC_UNIQUE_T_CSSP_XDNC_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressXdnc.COURSE_SECTION_STUDY_PROGRESS_XDNC, "KEY_t_course_section_study_progress_xdnc_unique_t_cssp_xdnc_member_section", CourseSectionStudyProgressXdnc.COURSE_SECTION_STUDY_PROGRESS_XDNC.SECTION_ID, CourseSectionStudyProgressXdnc.COURSE_SECTION_STUDY_PROGRESS_XDNC.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressXdnsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XDNS_PRIMARY = createUniqueKey(CourseSectionStudyProgressXdns.COURSE_SECTION_STUDY_PROGRESS_XDNS, "KEY_t_course_section_study_progress_xdns_PRIMARY", CourseSectionStudyProgressXdns.COURSE_SECTION_STUDY_PROGRESS_XDNS.ID);
        public static final UniqueKey<CourseSectionStudyProgressXdnsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XDNS_UNIQUE_T_CSSP_XDNS_SECTION_MEMBER = createUniqueKey(CourseSectionStudyProgressXdns.COURSE_SECTION_STUDY_PROGRESS_XDNS, "KEY_t_course_section_study_progress_xdns_unique_t_cssp_xdns_section_member", CourseSectionStudyProgressXdns.COURSE_SECTION_STUDY_PROGRESS_XDNS.SECTION_ID, CourseSectionStudyProgressXdns.COURSE_SECTION_STUDY_PROGRESS_XDNS.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressZhztsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZHZTS_PRIMARY = createUniqueKey(CourseSectionStudyProgressZhzts.COURSE_SECTION_STUDY_PROGRESS_ZHZTS, "KEY_t_course_section_study_progress_zhzts_PRIMARY", CourseSectionStudyProgressZhzts.COURSE_SECTION_STUDY_PROGRESS_ZHZTS.ID);
        public static final UniqueKey<CourseSectionStudyProgressZhztsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZHZTS_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressZhzts.COURSE_SECTION_STUDY_PROGRESS_ZHZTS, "KEY_t_course_section_study_progress_zhzts_unique_t_course_section_p_member_section", CourseSectionStudyProgressZhzts.COURSE_SECTION_STUDY_PROGRESS_ZHZTS.SECTION_ID, CourseSectionStudyProgressZhzts.COURSE_SECTION_STUDY_PROGRESS_ZHZTS.MEMBER_ID);
        public static final UniqueKey<CourseSequenceRecord> KEY_T_COURSE_SEQUENCE_PRIMARY = createUniqueKey(CourseSequence.COURSE_SEQUENCE, "KEY_t_course_sequence_PRIMARY", CourseSequence.COURSE_SEQUENCE.ID);
        public static final UniqueKey<CourseShelvesRecord> KEY_T_COURSE_SHELVES_PRIMARY = createUniqueKey(CourseShelves.COURSE_SHELVES, "KEY_t_course_shelves_PRIMARY", CourseShelves.COURSE_SHELVES.ID);
        public static final UniqueKey<CourseStudyProcess_2017Record> KEY_T_COURSE_STUDY_PROCESS_2017_PRIMARY = createUniqueKey(CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017, "KEY_t_course_study_process_2017_PRIMARY", CourseStudyProcess_2017.COURSE_STUDY_PROCESS_2017.ID);
        public static final UniqueKey<CourseStudyProgressRecord> KEY_T_COURSE_STUDY_PROGRESS_PRIMARY = createUniqueKey(CourseStudyProgress.COURSE_STUDY_PROGRESS, "KEY_t_course_study_progress_PRIMARY", CourseStudyProgress.COURSE_STUDY_PROGRESS.ID);
        public static final UniqueKey<CourseStudyProgressRecord> KEY_T_COURSE_STUDY_PROGRESS_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgress.COURSE_STUDY_PROGRESS, "KEY_t_course_study_progress_unique_t_course_progress_member_course", CourseStudyProgress.COURSE_STUDY_PROGRESS.MEMBER_ID, CourseStudyProgress.COURSE_STUDY_PROGRESS.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressAhRecord> KEY_T_COURSE_STUDY_PROGRESS_AH_PRIMARY = createUniqueKey(CourseStudyProgressAh.COURSE_STUDY_PROGRESS_AH, "KEY_t_course_study_progress_ah_PRIMARY", CourseStudyProgressAh.COURSE_STUDY_PROGRESS_AH.ID);
        public static final UniqueKey<CourseStudyProgressAhRecord> KEY_T_COURSE_STUDY_PROGRESS_AH_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressAh.COURSE_STUDY_PROGRESS_AH, "KEY_t_course_study_progress_ah_unique_t_course_progress_member_course", CourseStudyProgressAh.COURSE_STUDY_PROGRESS_AH.MEMBER_ID, CourseStudyProgressAh.COURSE_STUDY_PROGRESS_AH.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressBjRecord> KEY_T_COURSE_STUDY_PROGRESS_BJ_PRIMARY = createUniqueKey(CourseStudyProgressBj.COURSE_STUDY_PROGRESS_BJ, "KEY_t_course_study_progress_bj_PRIMARY", CourseStudyProgressBj.COURSE_STUDY_PROGRESS_BJ.ID);
        public static final UniqueKey<CourseStudyProgressBjRecord> KEY_T_COURSE_STUDY_PROGRESS_BJ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressBj.COURSE_STUDY_PROGRESS_BJ, "KEY_t_course_study_progress_bj_unique_t_course_progress_member_course", CourseStudyProgressBj.COURSE_STUDY_PROGRESS_BJ.MEMBER_ID, CourseStudyProgressBj.COURSE_STUDY_PROGRESS_BJ.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressCmRecord> KEY_T_COURSE_STUDY_PROGRESS_CM_PRIMARY = createUniqueKey(CourseStudyProgressCm.COURSE_STUDY_PROGRESS_CM, "KEY_t_course_study_progress_cm_PRIMARY", CourseStudyProgressCm.COURSE_STUDY_PROGRESS_CM.ID);
        public static final UniqueKey<CourseStudyProgressCmRecord> KEY_T_COURSE_STUDY_PROGRESS_CM_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressCm.COURSE_STUDY_PROGRESS_CM, "KEY_t_course_study_progress_cm_unique_t_course_progress_member_course", CourseStudyProgressCm.COURSE_STUDY_PROGRESS_CM.MEMBER_ID, CourseStudyProgressCm.COURSE_STUDY_PROGRESS_CM.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressCqRecord> KEY_T_COURSE_STUDY_PROGRESS_CQ_PRIMARY = createUniqueKey(CourseStudyProgressCq.COURSE_STUDY_PROGRESS_CQ, "KEY_t_course_study_progress_cq_PRIMARY", CourseStudyProgressCq.COURSE_STUDY_PROGRESS_CQ.ID);
        public static final UniqueKey<CourseStudyProgressCqRecord> KEY_T_COURSE_STUDY_PROGRESS_CQ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressCq.COURSE_STUDY_PROGRESS_CQ, "KEY_t_course_study_progress_cq_unique_t_course_progress_member_course", CourseStudyProgressCq.COURSE_STUDY_PROGRESS_CQ.MEMBER_ID, CourseStudyProgressCq.COURSE_STUDY_PROGRESS_CQ.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressEbRecord> KEY_T_COURSE_STUDY_PROGRESS_EB_PRIMARY = createUniqueKey(CourseStudyProgressEb.COURSE_STUDY_PROGRESS_EB, "KEY_t_course_study_progress_eb_PRIMARY", CourseStudyProgressEb.COURSE_STUDY_PROGRESS_EB.ID);
        public static final UniqueKey<CourseStudyProgressEbRecord> KEY_T_COURSE_STUDY_PROGRESS_EB_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressEb.COURSE_STUDY_PROGRESS_EB, "KEY_t_course_study_progress_eb_unique_t_course_progress_member_course", CourseStudyProgressEb.COURSE_STUDY_PROGRESS_EB.MEMBER_ID, CourseStudyProgressEb.COURSE_STUDY_PROGRESS_EB.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressFjRecord> KEY_T_COURSE_STUDY_PROGRESS_FJ_PRIMARY = createUniqueKey(CourseStudyProgressFj.COURSE_STUDY_PROGRESS_FJ, "KEY_t_course_study_progress_fj_PRIMARY", CourseStudyProgressFj.COURSE_STUDY_PROGRESS_FJ.ID);
        public static final UniqueKey<CourseStudyProgressFjRecord> KEY_T_COURSE_STUDY_PROGRESS_FJ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressFj.COURSE_STUDY_PROGRESS_FJ, "KEY_t_course_study_progress_fj_unique_t_course_progress_member_course", CourseStudyProgressFj.COURSE_STUDY_PROGRESS_FJ.MEMBER_ID, CourseStudyProgressFj.COURSE_STUDY_PROGRESS_FJ.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressGdRecord> KEY_T_COURSE_STUDY_PROGRESS_GD_PRIMARY = createUniqueKey(CourseStudyProgressGd.COURSE_STUDY_PROGRESS_GD, "KEY_t_course_study_progress_gd_PRIMARY", CourseStudyProgressGd.COURSE_STUDY_PROGRESS_GD.ID);
        public static final UniqueKey<CourseStudyProgressGdRecord> KEY_T_COURSE_STUDY_PROGRESS_GD_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressGd.COURSE_STUDY_PROGRESS_GD, "KEY_t_course_study_progress_gd_unique_t_course_progress_member_course", CourseStudyProgressGd.COURSE_STUDY_PROGRESS_GD.MEMBER_ID, CourseStudyProgressGd.COURSE_STUDY_PROGRESS_GD.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressGsRecord> KEY_T_COURSE_STUDY_PROGRESS_GS_PRIMARY = createUniqueKey(CourseStudyProgressGs.COURSE_STUDY_PROGRESS_GS, "KEY_t_course_study_progress_gs_PRIMARY", CourseStudyProgressGs.COURSE_STUDY_PROGRESS_GS.ID);
        public static final UniqueKey<CourseStudyProgressGsRecord> KEY_T_COURSE_STUDY_PROGRESS_GS_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressGs.COURSE_STUDY_PROGRESS_GS, "KEY_t_course_study_progress_gs_unique_t_course_progress_member_course", CourseStudyProgressGs.COURSE_STUDY_PROGRESS_GS.MEMBER_ID, CourseStudyProgressGs.COURSE_STUDY_PROGRESS_GS.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressGxRecord> KEY_T_COURSE_STUDY_PROGRESS_GX_PRIMARY = createUniqueKey(CourseStudyProgressGx.COURSE_STUDY_PROGRESS_GX, "KEY_t_course_study_progress_gx_PRIMARY", CourseStudyProgressGx.COURSE_STUDY_PROGRESS_GX.ID);
        public static final UniqueKey<CourseStudyProgressGxRecord> KEY_T_COURSE_STUDY_PROGRESS_GX_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressGx.COURSE_STUDY_PROGRESS_GX, "KEY_t_course_study_progress_gx_unique_t_course_progress_member_course", CourseStudyProgressGx.COURSE_STUDY_PROGRESS_GX.MEMBER_ID, CourseStudyProgressGx.COURSE_STUDY_PROGRESS_GX.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressGzRecord> KEY_T_COURSE_STUDY_PROGRESS_GZ_PRIMARY = createUniqueKey(CourseStudyProgressGz.COURSE_STUDY_PROGRESS_GZ, "KEY_t_course_study_progress_gz_PRIMARY", CourseStudyProgressGz.COURSE_STUDY_PROGRESS_GZ.ID);
        public static final UniqueKey<CourseStudyProgressGzRecord> KEY_T_COURSE_STUDY_PROGRESS_GZ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressGz.COURSE_STUDY_PROGRESS_GZ, "KEY_t_course_study_progress_gz_unique_t_course_progress_member_course", CourseStudyProgressGz.COURSE_STUDY_PROGRESS_GZ.MEMBER_ID, CourseStudyProgressGz.COURSE_STUDY_PROGRESS_GZ.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressHbRecord> KEY_T_COURSE_STUDY_PROGRESS_HB_PRIMARY = createUniqueKey(CourseStudyProgressHb.COURSE_STUDY_PROGRESS_HB, "KEY_t_course_study_progress_hb_PRIMARY", CourseStudyProgressHb.COURSE_STUDY_PROGRESS_HB.ID);
        public static final UniqueKey<CourseStudyProgressHbRecord> KEY_T_COURSE_STUDY_PROGRESS_HB_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressHb.COURSE_STUDY_PROGRESS_HB, "KEY_t_course_study_progress_hb_unique_t_course_progress_member_course", CourseStudyProgressHb.COURSE_STUDY_PROGRESS_HB.MEMBER_ID, CourseStudyProgressHb.COURSE_STUDY_PROGRESS_HB.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressHlRecord> KEY_T_COURSE_STUDY_PROGRESS_HL_PRIMARY = createUniqueKey(CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL, "KEY_t_course_study_progress_hl_PRIMARY", CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.ID);
        public static final UniqueKey<CourseStudyProgressHlRecord> KEY_T_COURSE_STUDY_PROGRESS_HL_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL, "KEY_t_course_study_progress_hl_unique_t_course_progress_member_course", CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.MEMBER_ID, CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressHnRecord> KEY_T_COURSE_STUDY_PROGRESS_HN_PRIMARY = createUniqueKey(CourseStudyProgressHn.COURSE_STUDY_PROGRESS_HN, "KEY_t_course_study_progress_hn_PRIMARY", CourseStudyProgressHn.COURSE_STUDY_PROGRESS_HN.ID);
        public static final UniqueKey<CourseStudyProgressHnRecord> KEY_T_COURSE_STUDY_PROGRESS_HN_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressHn.COURSE_STUDY_PROGRESS_HN, "KEY_t_course_study_progress_hn_unique_t_course_progress_member_course", CourseStudyProgressHn.COURSE_STUDY_PROGRESS_HN.MEMBER_ID, CourseStudyProgressHn.COURSE_STUDY_PROGRESS_HN.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressJlRecord> KEY_T_COURSE_STUDY_PROGRESS_JL_PRIMARY = createUniqueKey(CourseStudyProgressJl.COURSE_STUDY_PROGRESS_JL, "KEY_t_course_study_progress_jl_PRIMARY", CourseStudyProgressJl.COURSE_STUDY_PROGRESS_JL.ID);
        public static final UniqueKey<CourseStudyProgressJlRecord> KEY_T_COURSE_STUDY_PROGRESS_JL_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressJl.COURSE_STUDY_PROGRESS_JL, "KEY_t_course_study_progress_jl_unique_t_course_progress_member_course", CourseStudyProgressJl.COURSE_STUDY_PROGRESS_JL.MEMBER_ID, CourseStudyProgressJl.COURSE_STUDY_PROGRESS_JL.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressJsRecord> KEY_T_COURSE_STUDY_PROGRESS_JS_PRIMARY = createUniqueKey(CourseStudyProgressJs.COURSE_STUDY_PROGRESS_JS, "KEY_t_course_study_progress_js_PRIMARY", CourseStudyProgressJs.COURSE_STUDY_PROGRESS_JS.ID);
        public static final UniqueKey<CourseStudyProgressJsRecord> KEY_T_COURSE_STUDY_PROGRESS_JS_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressJs.COURSE_STUDY_PROGRESS_JS, "KEY_t_course_study_progress_js_unique_t_course_progress_member_course", CourseStudyProgressJs.COURSE_STUDY_PROGRESS_JS.MEMBER_ID, CourseStudyProgressJs.COURSE_STUDY_PROGRESS_JS.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressJxRecord> KEY_T_COURSE_STUDY_PROGRESS_JX_PRIMARY = createUniqueKey(CourseStudyProgressJx.COURSE_STUDY_PROGRESS_JX, "KEY_t_course_study_progress_jx_PRIMARY", CourseStudyProgressJx.COURSE_STUDY_PROGRESS_JX.ID);
        public static final UniqueKey<CourseStudyProgressJxRecord> KEY_T_COURSE_STUDY_PROGRESS_JX_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressJx.COURSE_STUDY_PROGRESS_JX, "KEY_t_course_study_progress_jx_unique_t_course_progress_member_course", CourseStudyProgressJx.COURSE_STUDY_PROGRESS_JX.MEMBER_ID, CourseStudyProgressJx.COURSE_STUDY_PROGRESS_JX.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressLnRecord> KEY_T_COURSE_STUDY_PROGRESS_LN_PRIMARY = createUniqueKey(CourseStudyProgressLn.COURSE_STUDY_PROGRESS_LN, "KEY_t_course_study_progress_ln_PRIMARY", CourseStudyProgressLn.COURSE_STUDY_PROGRESS_LN.ID);
        public static final UniqueKey<CourseStudyProgressLnRecord> KEY_T_COURSE_STUDY_PROGRESS_LN_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressLn.COURSE_STUDY_PROGRESS_LN, "KEY_t_course_study_progress_ln_unique_t_course_progress_member_course", CourseStudyProgressLn.COURSE_STUDY_PROGRESS_LN.MEMBER_ID, CourseStudyProgressLn.COURSE_STUDY_PROGRESS_LN.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressNmRecord> KEY_T_COURSE_STUDY_PROGRESS_NM_PRIMARY = createUniqueKey(CourseStudyProgressNm.COURSE_STUDY_PROGRESS_NM, "KEY_t_course_study_progress_nm_PRIMARY", CourseStudyProgressNm.COURSE_STUDY_PROGRESS_NM.ID);
        public static final UniqueKey<CourseStudyProgressNmRecord> KEY_T_COURSE_STUDY_PROGRESS_NM_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressNm.COURSE_STUDY_PROGRESS_NM, "KEY_t_course_study_progress_nm_unique_t_course_progress_member_course", CourseStudyProgressNm.COURSE_STUDY_PROGRESS_NM.MEMBER_ID, CourseStudyProgressNm.COURSE_STUDY_PROGRESS_NM.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressNxRecord> KEY_T_COURSE_STUDY_PROGRESS_NX_PRIMARY = createUniqueKey(CourseStudyProgressNx.COURSE_STUDY_PROGRESS_NX, "KEY_t_course_study_progress_nx_PRIMARY", CourseStudyProgressNx.COURSE_STUDY_PROGRESS_NX.ID);
        public static final UniqueKey<CourseStudyProgressNxRecord> KEY_T_COURSE_STUDY_PROGRESS_NX_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressNx.COURSE_STUDY_PROGRESS_NX, "KEY_t_course_study_progress_nx_unique_t_course_progress_member_course", CourseStudyProgressNx.COURSE_STUDY_PROGRESS_NX.MEMBER_ID, CourseStudyProgressNx.COURSE_STUDY_PROGRESS_NX.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressOtherRecord> KEY_T_COURSE_STUDY_PROGRESS_OTHER_PRIMARY = createUniqueKey(CourseStudyProgressOther.COURSE_STUDY_PROGRESS_OTHER, "KEY_t_course_study_progress_other_PRIMARY", CourseStudyProgressOther.COURSE_STUDY_PROGRESS_OTHER.ID);
        public static final UniqueKey<CourseStudyProgressOtherRecord> KEY_T_COURSE_STUDY_PROGRESS_OTHER_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressOther.COURSE_STUDY_PROGRESS_OTHER, "KEY_t_course_study_progress_other_unique_t_course_progress_member_course", CourseStudyProgressOther.COURSE_STUDY_PROGRESS_OTHER.MEMBER_ID, CourseStudyProgressOther.COURSE_STUDY_PROGRESS_OTHER.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressQhRecord> KEY_T_COURSE_STUDY_PROGRESS_QH_PRIMARY = createUniqueKey(CourseStudyProgressQh.COURSE_STUDY_PROGRESS_QH, "KEY_t_course_study_progress_qh_PRIMARY", CourseStudyProgressQh.COURSE_STUDY_PROGRESS_QH.ID);
        public static final UniqueKey<CourseStudyProgressQhRecord> KEY_T_COURSE_STUDY_PROGRESS_QH_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressQh.COURSE_STUDY_PROGRESS_QH, "KEY_t_course_study_progress_qh_unique_t_course_progress_member_course", CourseStudyProgressQh.COURSE_STUDY_PROGRESS_QH.MEMBER_ID, CourseStudyProgressQh.COURSE_STUDY_PROGRESS_QH.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressQoRecord> KEY_T_COURSE_STUDY_PROGRESS_QO_PRIMARY = createUniqueKey(CourseStudyProgressQo.COURSE_STUDY_PROGRESS_QO, "KEY_t_course_study_progress_qo_PRIMARY", CourseStudyProgressQo.COURSE_STUDY_PROGRESS_QO.ID);
        public static final UniqueKey<CourseStudyProgressQoRecord> KEY_T_COURSE_STUDY_PROGRESS_QO_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressQo.COURSE_STUDY_PROGRESS_QO, "KEY_t_course_study_progress_qo_unique_t_course_progress_member_course", CourseStudyProgressQo.COURSE_STUDY_PROGRESS_QO.MEMBER_ID, CourseStudyProgressQo.COURSE_STUDY_PROGRESS_QO.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressScRecord> KEY_T_COURSE_STUDY_PROGRESS_SC_PRIMARY = createUniqueKey(CourseStudyProgressSc.COURSE_STUDY_PROGRESS_SC, "KEY_t_course_study_progress_sc_PRIMARY", CourseStudyProgressSc.COURSE_STUDY_PROGRESS_SC.ID);
        public static final UniqueKey<CourseStudyProgressScRecord> KEY_T_COURSE_STUDY_PROGRESS_SC_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressSc.COURSE_STUDY_PROGRESS_SC, "KEY_t_course_study_progress_sc_unique_t_course_progress_member_course", CourseStudyProgressSc.COURSE_STUDY_PROGRESS_SC.MEMBER_ID, CourseStudyProgressSc.COURSE_STUDY_PROGRESS_SC.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressSdRecord> KEY_T_COURSE_STUDY_PROGRESS_SD_PRIMARY = createUniqueKey(CourseStudyProgressSd.COURSE_STUDY_PROGRESS_SD, "KEY_t_course_study_progress_sd_PRIMARY", CourseStudyProgressSd.COURSE_STUDY_PROGRESS_SD.ID);
        public static final UniqueKey<CourseStudyProgressSdRecord> KEY_T_COURSE_STUDY_PROGRESS_SD_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressSd.COURSE_STUDY_PROGRESS_SD, "KEY_t_course_study_progress_sd_unique_t_course_progress_member_course", CourseStudyProgressSd.COURSE_STUDY_PROGRESS_SD.MEMBER_ID, CourseStudyProgressSd.COURSE_STUDY_PROGRESS_SD.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressShRecord> KEY_T_COURSE_STUDY_PROGRESS_SH_PRIMARY = createUniqueKey(CourseStudyProgressSh.COURSE_STUDY_PROGRESS_SH, "KEY_t_course_study_progress_sh_PRIMARY", CourseStudyProgressSh.COURSE_STUDY_PROGRESS_SH.ID);
        public static final UniqueKey<CourseStudyProgressShRecord> KEY_T_COURSE_STUDY_PROGRESS_SH_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressSh.COURSE_STUDY_PROGRESS_SH, "KEY_t_course_study_progress_sh_unique_t_course_progress_member_course", CourseStudyProgressSh.COURSE_STUDY_PROGRESS_SH.MEMBER_ID, CourseStudyProgressSh.COURSE_STUDY_PROGRESS_SH.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressSnRecord> KEY_T_COURSE_STUDY_PROGRESS_SN_PRIMARY = createUniqueKey(CourseStudyProgressSn.COURSE_STUDY_PROGRESS_SN, "KEY_t_course_study_progress_sn_PRIMARY", CourseStudyProgressSn.COURSE_STUDY_PROGRESS_SN.ID);
        public static final UniqueKey<CourseStudyProgressSnRecord> KEY_T_COURSE_STUDY_PROGRESS_SN_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressSn.COURSE_STUDY_PROGRESS_SN, "KEY_t_course_study_progress_sn_unique_t_course_progress_member_course", CourseStudyProgressSn.COURSE_STUDY_PROGRESS_SN.MEMBER_ID, CourseStudyProgressSn.COURSE_STUDY_PROGRESS_SN.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressSxRecord> KEY_T_COURSE_STUDY_PROGRESS_SX_PRIMARY = createUniqueKey(CourseStudyProgressSx.COURSE_STUDY_PROGRESS_SX, "KEY_t_course_study_progress_sx_PRIMARY", CourseStudyProgressSx.COURSE_STUDY_PROGRESS_SX.ID);
        public static final UniqueKey<CourseStudyProgressSxRecord> KEY_T_COURSE_STUDY_PROGRESS_SX_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressSx.COURSE_STUDY_PROGRESS_SX, "KEY_t_course_study_progress_sx_unique_t_course_progress_member_course", CourseStudyProgressSx.COURSE_STUDY_PROGRESS_SX.MEMBER_ID, CourseStudyProgressSx.COURSE_STUDY_PROGRESS_SX.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressTjRecord> KEY_T_COURSE_STUDY_PROGRESS_TJ_PRIMARY = createUniqueKey(CourseStudyProgressTj.COURSE_STUDY_PROGRESS_TJ, "KEY_t_course_study_progress_tj_PRIMARY", CourseStudyProgressTj.COURSE_STUDY_PROGRESS_TJ.ID);
        public static final UniqueKey<CourseStudyProgressTjRecord> KEY_T_COURSE_STUDY_PROGRESS_TJ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressTj.COURSE_STUDY_PROGRESS_TJ, "KEY_t_course_study_progress_tj_unique_t_course_progress_member_course", CourseStudyProgressTj.COURSE_STUDY_PROGRESS_TJ.MEMBER_ID, CourseStudyProgressTj.COURSE_STUDY_PROGRESS_TJ.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressXjRecord> KEY_T_COURSE_STUDY_PROGRESS_XJ_PRIMARY = createUniqueKey(CourseStudyProgressXj.COURSE_STUDY_PROGRESS_XJ, "KEY_t_course_study_progress_xj_PRIMARY", CourseStudyProgressXj.COURSE_STUDY_PROGRESS_XJ.ID);
        public static final UniqueKey<CourseStudyProgressXjRecord> KEY_T_COURSE_STUDY_PROGRESS_XJ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressXj.COURSE_STUDY_PROGRESS_XJ, "KEY_t_course_study_progress_xj_unique_t_course_progress_member_course", CourseStudyProgressXj.COURSE_STUDY_PROGRESS_XJ.MEMBER_ID, CourseStudyProgressXj.COURSE_STUDY_PROGRESS_XJ.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressXnRecord> KEY_T_COURSE_STUDY_PROGRESS_XN_PRIMARY = createUniqueKey(CourseStudyProgressXn.COURSE_STUDY_PROGRESS_XN, "KEY_t_course_study_progress_xn_PRIMARY", CourseStudyProgressXn.COURSE_STUDY_PROGRESS_XN.ID);
        public static final UniqueKey<CourseStudyProgressXnRecord> KEY_T_COURSE_STUDY_PROGRESS_XN_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressXn.COURSE_STUDY_PROGRESS_XN, "KEY_t_course_study_progress_xn_unique_t_course_progress_member_course", CourseStudyProgressXn.COURSE_STUDY_PROGRESS_XN.MEMBER_ID, CourseStudyProgressXn.COURSE_STUDY_PROGRESS_XN.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressXzRecord> KEY_T_COURSE_STUDY_PROGRESS_XZ_PRIMARY = createUniqueKey(CourseStudyProgressXz.COURSE_STUDY_PROGRESS_XZ, "KEY_t_course_study_progress_xz_PRIMARY", CourseStudyProgressXz.COURSE_STUDY_PROGRESS_XZ.ID);
        public static final UniqueKey<CourseStudyProgressXzRecord> KEY_T_COURSE_STUDY_PROGRESS_XZ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressXz.COURSE_STUDY_PROGRESS_XZ, "KEY_t_course_study_progress_xz_unique_t_course_progress_member_course", CourseStudyProgressXz.COURSE_STUDY_PROGRESS_XZ.MEMBER_ID, CourseStudyProgressXz.COURSE_STUDY_PROGRESS_XZ.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressYnRecord> KEY_T_COURSE_STUDY_PROGRESS_YN_PRIMARY = createUniqueKey(CourseStudyProgressYn.COURSE_STUDY_PROGRESS_YN, "KEY_t_course_study_progress_yn_PRIMARY", CourseStudyProgressYn.COURSE_STUDY_PROGRESS_YN.ID);
        public static final UniqueKey<CourseStudyProgressYnRecord> KEY_T_COURSE_STUDY_PROGRESS_YN_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressYn.COURSE_STUDY_PROGRESS_YN, "KEY_t_course_study_progress_yn_unique_t_course_progress_member_course", CourseStudyProgressYn.COURSE_STUDY_PROGRESS_YN.MEMBER_ID, CourseStudyProgressYn.COURSE_STUDY_PROGRESS_YN.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressZgttRecord> KEY_T_COURSE_STUDY_PROGRESS_ZGTT_PRIMARY = createUniqueKey(CourseStudyProgressZgtt.COURSE_STUDY_PROGRESS_ZGTT, "KEY_t_course_study_progress_zgtt_PRIMARY", CourseStudyProgressZgtt.COURSE_STUDY_PROGRESS_ZGTT.ID);
        public static final UniqueKey<CourseStudyProgressZgttRecord> KEY_T_COURSE_STUDY_PROGRESS_ZGTT_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressZgtt.COURSE_STUDY_PROGRESS_ZGTT, "KEY_t_course_study_progress_zgtt_unique_t_course_progress_member_course", CourseStudyProgressZgtt.COURSE_STUDY_PROGRESS_ZGTT.MEMBER_ID, CourseStudyProgressZgtt.COURSE_STUDY_PROGRESS_ZGTT.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressZjRecord> KEY_T_COURSE_STUDY_PROGRESS_ZJ_PRIMARY = createUniqueKey(CourseStudyProgressZj.COURSE_STUDY_PROGRESS_ZJ, "KEY_t_course_study_progress_zj_PRIMARY", CourseStudyProgressZj.COURSE_STUDY_PROGRESS_ZJ.ID);
        public static final UniqueKey<CourseStudyProgressZjRecord> KEY_T_COURSE_STUDY_PROGRESS_ZJ_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressZj.COURSE_STUDY_PROGRESS_ZJ, "KEY_t_course_study_progress_zj_unique_t_course_progress_member_course", CourseStudyProgressZj.COURSE_STUDY_PROGRESS_ZJ.MEMBER_ID, CourseStudyProgressZj.COURSE_STUDY_PROGRESS_ZJ.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressZxRecord> KEY_T_COURSE_STUDY_PROGRESS_ZX_PRIMARY = createUniqueKey(CourseStudyProgressZx.COURSE_STUDY_PROGRESS_ZX, "KEY_t_course_study_progress_zx_PRIMARY", CourseStudyProgressZx.COURSE_STUDY_PROGRESS_ZX.ID);
        public static final UniqueKey<CourseStudyProgressZxRecord> KEY_T_COURSE_STUDY_PROGRESS_ZX_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressZx.COURSE_STUDY_PROGRESS_ZX, "KEY_t_course_study_progress_zx_unique_t_course_progress_member_course", CourseStudyProgressZx.COURSE_STUDY_PROGRESS_ZX.MEMBER_ID, CourseStudyProgressZx.COURSE_STUDY_PROGRESS_ZX.COURSE_ID);
        public static final UniqueKey<CourseTopicRecord> KEY_T_COURSE_TOPIC_PRIMARY = createUniqueKey(CourseTopic.COURSE_TOPIC, "KEY_t_course_topic_PRIMARY", CourseTopic.COURSE_TOPIC.ID);
        public static final UniqueKey<CourseVersionRecord> KEY_T_COURSE_VERSION_PRIMARY = createUniqueKey(CourseVersion.COURSE_VERSION, "KEY_t_course_version_PRIMARY", CourseVersion.COURSE_VERSION.ID);
        public static final UniqueKey<DbaAnalyzeTableIndexRecord> KEY_T_DBA_ANALYZE_TABLE_INDEX_PRIMARY = createUniqueKey(DbaAnalyzeTableIndex.DBA_ANALYZE_TABLE_INDEX, "KEY_t_dba_analyze_table_index_PRIMARY", DbaAnalyzeTableIndex.DBA_ANALYZE_TABLE_INDEX.TID);
        public static final UniqueKey<DjClassifyRecord> KEY_T_DJ_CLASSIFY_PRIMARY = createUniqueKey(DjClassify.DJ_CLASSIFY, "KEY_t_dj_classify_PRIMARY", DjClassify.DJ_CLASSIFY.ID);
        public static final UniqueKey<DjResourceRecord> KEY_T_DJ_RESOURCE_PRIMARY = createUniqueKey(DjResource.DJ_RESOURCE, "KEY_t_dj_resource_PRIMARY", DjResource.DJ_RESOURCE.ID);
        public static final UniqueKey<ExamRecord> KEY_T_EXAM_PRIMARY = createUniqueKey(Exam.EXAM, "KEY_t_exam_PRIMARY", Exam.EXAM.ID);
        public static final UniqueKey<ExamRecordRecord> KEY_T_EXAM_RECORD_PRIMARY = createUniqueKey(com.zxy.product.course.jooq.tables.ExamRecord.EXAM_RECORD, "KEY_t_exam_record_PRIMARY", com.zxy.product.course.jooq.tables.ExamRecord.EXAM_RECORD.ID);
        public static final UniqueKey<GenseeBusinessRecord> KEY_T_GENSEE_BUSINESS_PRIMARY = createUniqueKey(GenseeBusiness.GENSEE_BUSINESS, "KEY_t_gensee_business_PRIMARY", GenseeBusiness.GENSEE_BUSINESS.ID);
        public static final UniqueKey<GenseeBusinessProgressRecord> KEY_T_GENSEE_BUSINESS_PROGRESS_PRIMARY = createUniqueKey(GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS, "KEY_t_gensee_business_progress_PRIMARY", GenseeBusinessProgress.GENSEE_BUSINESS_PROGRESS.ID);
        public static final UniqueKey<GenseeLecturerRecord> KEY_T_GENSEE_LECTURER_PRIMARY = createUniqueKey(GenseeLecturer.GENSEE_LECTURER, "KEY_t_gensee_lecturer_PRIMARY", GenseeLecturer.GENSEE_LECTURER.ID);
        public static final UniqueKey<GenseeSubscriptionRecord> KEY_T_GENSEE_SUBSCRIPTION_PRIMARY = createUniqueKey(GenseeSubscription.GENSEE_SUBSCRIPTION, "KEY_t_gensee_subscription_PRIMARY", GenseeSubscription.GENSEE_SUBSCRIPTION.ID);
        public static final UniqueKey<GenseeTopicRecord> KEY_T_GENSEE_TOPIC_PRIMARY = createUniqueKey(GenseeTopic.GENSEE_TOPIC, "KEY_t_gensee_topic_PRIMARY", GenseeTopic.GENSEE_TOPIC.ID);
        public static final UniqueKey<GenseeUserAccessRecord> KEY_T_GENSEE_USER_ACCESS_PRIMARY = createUniqueKey(GenseeUserAccess.GENSEE_USER_ACCESS, "KEY_t_gensee_user_access_PRIMARY", GenseeUserAccess.GENSEE_USER_ACCESS.ID);
        public static final UniqueKey<GenseeUserAccessRecord> KEY_T_GENSEE_USER_ACCESS_UNIQUE_T_GENSEE_USER_ACCESS_MEMBER_GENSEE = createUniqueKey(GenseeUserAccess.GENSEE_USER_ACCESS, "KEY_t_gensee_user_access_unique_t_gensee_user_access_member_gensee", GenseeUserAccess.GENSEE_USER_ACCESS.USER_ID, GenseeUserAccess.GENSEE_USER_ACCESS.GENSEE_ID);
        public static final UniqueKey<GenseeUserJoinHistoryRecord> KEY_T_GENSEE_USER_JOIN_HISTORY_PRIMARY = createUniqueKey(GenseeUserJoinHistory.GENSEE_USER_JOIN_HISTORY, "KEY_t_gensee_user_join_history_PRIMARY", GenseeUserJoinHistory.GENSEE_USER_JOIN_HISTORY.ID);
        public static final UniqueKey<GenseeWebCastRecord> KEY_T_GENSEE_WEB_CAST_PRIMARY = createUniqueKey(GenseeWebCast.GENSEE_WEB_CAST, "KEY_t_gensee_web_cast_PRIMARY", GenseeWebCast.GENSEE_WEB_CAST.ID);
        public static final UniqueKey<GrantDetailRecord> KEY_T_GRANT_DETAIL_PRIMARY = createUniqueKey(GrantDetail.GRANT_DETAIL, "KEY_t_grant_detail_PRIMARY", GrantDetail.GRANT_DETAIL.ID);
        public static final UniqueKey<JobRecord> KEY_T_JOB_PRIMARY = createUniqueKey(Job.JOB, "KEY_t_job_PRIMARY", Job.JOB.ID);
        public static final UniqueKey<KnowledgeCategoryRecord> KEY_T_KNOWLEDGE_CATEGORY_PRIMARY = createUniqueKey(KnowledgeCategory.KNOWLEDGE_CATEGORY, "KEY_t_knowledge_category_PRIMARY", KnowledgeCategory.KNOWLEDGE_CATEGORY.ID);
        public static final UniqueKey<KnowledgeDownRecordRecord> KEY_T_KNOWLEDGE_DOWN_RECORD_PRIMARY = createUniqueKey(KnowledgeDownRecord.KNOWLEDGE_DOWN_RECORD, "KEY_t_knowledge_down_record_PRIMARY", KnowledgeDownRecord.KNOWLEDGE_DOWN_RECORD.ID);
        public static final UniqueKey<KnowledgeInfoRecord> KEY_T_KNOWLEDGE_INFO_PRIMARY = createUniqueKey(KnowledgeInfo.KNOWLEDGE_INFO, "KEY_t_knowledge_info_PRIMARY", KnowledgeInfo.KNOWLEDGE_INFO.ID);
        public static final UniqueKey<KnowledgeMonthListRecord> KEY_T_KNOWLEDGE_MONTH_LIST_PRIMARY = createUniqueKey(KnowledgeMonthList.KNOWLEDGE_MONTH_LIST, "KEY_t_knowledge_month_list_PRIMARY", KnowledgeMonthList.KNOWLEDGE_MONTH_LIST.ID);
        public static final UniqueKey<KnowledgeTopicRecord> KEY_T_KNOWLEDGE_TOPIC_PRIMARY = createUniqueKey(KnowledgeTopic.KNOWLEDGE_TOPIC, "KEY_t_knowledge_topic_PRIMARY", KnowledgeTopic.KNOWLEDGE_TOPIC.ID);
        public static final UniqueKey<KnowledgeUseRecordRecord> KEY_T_KNOWLEDGE_USE_RECORD_PRIMARY = createUniqueKey(KnowledgeUseRecord.KNOWLEDGE_USE_RECORD, "KEY_t_knowledge_use_record_PRIMARY", KnowledgeUseRecord.KNOWLEDGE_USE_RECORD.ID);
        public static final UniqueKey<KnowledgeViewRecordRecord> KEY_T_KNOWLEDGE_VIEW_RECORD_PRIMARY = createUniqueKey(KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD, "KEY_t_knowledge_view_record_PRIMARY", KnowledgeViewRecord.KNOWLEDGE_VIEW_RECORD.ID);
        public static final UniqueKey<MemberRecord> KEY_T_MEMBER_PRIMARY = createUniqueKey(Member.MEMBER, "KEY_t_member_PRIMARY", Member.MEMBER.ID);
        public static final UniqueKey<MemberCourseMonthRecord> KEY_T_MEMBER_COURSE_MONTH_PRIMARY = createUniqueKey(MemberCourseMonth.MEMBER_COURSE_MONTH, "KEY_t_member_course_month_PRIMARY", MemberCourseMonth.MEMBER_COURSE_MONTH.ID);
        public static final UniqueKey<MemberDetailRecord> KEY_T_MEMBER_DETAIL_PRIMARY = createUniqueKey(MemberDetail.MEMBER_DETAIL, "KEY_t_member_detail_PRIMARY", MemberDetail.MEMBER_DETAIL.ID);
        public static final UniqueKey<MemberKnowledgeMonthRecord> KEY_T_MEMBER_KNOWLEDGE_MONTH_PRIMARY = createUniqueKey(MemberKnowledgeMonth.MEMBER_KNOWLEDGE_MONTH, "KEY_t_member_knowledge_month_PRIMARY", MemberKnowledgeMonth.MEMBER_KNOWLEDGE_MONTH.ID);
        public static final UniqueKey<MemberPartyRecord> KEY_T_MEMBER_PARTY_PRIMARY = createUniqueKey(MemberParty.MEMBER_PARTY, "KEY_t_member_party_PRIMARY", MemberParty.MEMBER_PARTY.ID);
        public static final UniqueKey<MemberStatisticsRecord> KEY_T_MEMBER_STATISTICS_PRIMARY = createUniqueKey(MemberStatistics.MEMBER_STATISTICS, "KEY_t_member_statistics_PRIMARY", MemberStatistics.MEMBER_STATISTICS.ID);
        public static final UniqueKey<MemberStatisticsArchivesRecord> KEY_T_MEMBER_STATISTICS_ARCHIVES_PRIMARY = createUniqueKey(MemberStatisticsArchives.MEMBER_STATISTICS_ARCHIVES, "KEY_t_member_statistics_archives_PRIMARY", MemberStatisticsArchives.MEMBER_STATISTICS_ARCHIVES.ID);
        public static final UniqueKey<NoticeRecord> KEY_T_NOTICE_PRIMARY = createUniqueKey(Notice.NOTICE, "KEY_t_notice_PRIMARY", Notice.NOTICE.ID);
        public static final UniqueKey<OfflineClassRecord> KEY_T_OFFLINE_CLASS_PRIMARY = createUniqueKey(OfflineClass.OFFLINE_CLASS, "KEY_t_offline_class_PRIMARY", OfflineClass.OFFLINE_CLASS.ID);
        public static final UniqueKey<OfflineCourseQuestionnaireRecord> KEY_T_OFFLINE_COURSE_QUESTIONNAIRE_PRIMARY = createUniqueKey(OfflineCourseQuestionnaire.OFFLINE_COURSE_QUESTIONNAIRE, "KEY_t_offline_course_questionnaire_PRIMARY", OfflineCourseQuestionnaire.OFFLINE_COURSE_QUESTIONNAIRE.ID);
        public static final UniqueKey<OfflineCourseQuestionnaireChapterRecord> KEY_T_OFFLINE_COURSE_QUESTIONNAIRE_CHAPTER_PRIMARY = createUniqueKey(OfflineCourseQuestionnaireChapter.OFFLINE_COURSE_QUESTIONNAIRE_CHAPTER, "KEY_t_offline_course_questionnaire_chapter_PRIMARY", OfflineCourseQuestionnaireChapter.OFFLINE_COURSE_QUESTIONNAIRE_CHAPTER.ID);
        public static final UniqueKey<OfflineQuestionnaireAnswerRecord> KEY_T_OFFLINE_QUESTIONNAIRE_ANSWER_PRIMARY = createUniqueKey(OfflineQuestionnaireAnswer.OFFLINE_QUESTIONNAIRE_ANSWER, "KEY_t_offline_questionnaire_answer_PRIMARY", OfflineQuestionnaireAnswer.OFFLINE_QUESTIONNAIRE_ANSWER.ID);
        public static final UniqueKey<OnlineQuestionnaireAnswerRecord> KEY_T_ONLINE_QUESTIONNAIRE_ANSWER_PRIMARY = createUniqueKey(OnlineQuestionnaireAnswer.ONLINE_QUESTIONNAIRE_ANSWER, "KEY_t_online_questionnaire_answer_PRIMARY", OnlineQuestionnaireAnswer.ONLINE_QUESTIONNAIRE_ANSWER.ID);
        public static final UniqueKey<OrganizationRecord> KEY_T_ORGANIZATION_PRIMARY = createUniqueKey(Organization.ORGANIZATION, "KEY_t_organization_PRIMARY", Organization.ORGANIZATION.ID);
        public static final UniqueKey<OrganizationDetailRecord> KEY_T_ORGANIZATION_DETAIL_PRIMARY = createUniqueKey(OrganizationDetail.ORGANIZATION_DETAIL, "KEY_t_organization_detail_PRIMARY", OrganizationDetail.ORGANIZATION_DETAIL.ID);
        public static final UniqueKey<OrgTableCountRecord> KEY_T_ORG_TABLE_COUNT_PRIMARY = createUniqueKey(OrgTableCount.ORG_TABLE_COUNT, "KEY_t_org_table_count_PRIMARY", OrgTableCount.ORG_TABLE_COUNT.ID);
        public static final UniqueKey<PartyActivityMemberDayRecord> KEY_T_PARTY_ACTIVITY_MEMBER_DAY_PRIMARY = createUniqueKey(PartyActivityMemberDay.PARTY_ACTIVITY_MEMBER_DAY, "KEY_t_party_activity_member_day_PRIMARY", PartyActivityMemberDay.PARTY_ACTIVITY_MEMBER_DAY.ID);
        public static final UniqueKey<PartyActivityMemberMonthRecord> KEY_T_PARTY_ACTIVITY_MEMBER_MONTH_PRIMARY = createUniqueKey(PartyActivityMemberMonth.PARTY_ACTIVITY_MEMBER_MONTH, "KEY_t_party_activity_member_month_PRIMARY", PartyActivityMemberMonth.PARTY_ACTIVITY_MEMBER_MONTH.ID);
        public static final UniqueKey<PartyActivityOrgDayRecord> KEY_T_PARTY_ACTIVITY_ORG_DAY_PRIMARY = createUniqueKey(PartyActivityOrgDay.PARTY_ACTIVITY_ORG_DAY, "KEY_t_party_activity_org_day_PRIMARY", PartyActivityOrgDay.PARTY_ACTIVITY_ORG_DAY.ID);
        public static final UniqueKey<PartyActivityOrgMonthRecord> KEY_T_PARTY_ACTIVITY_ORG_MONTH_PRIMARY = createUniqueKey(PartyActivityOrgMonth.PARTY_ACTIVITY_ORG_MONTH, "KEY_t_party_activity_org_month_PRIMARY", PartyActivityOrgMonth.PARTY_ACTIVITY_ORG_MONTH.ID);
        public static final UniqueKey<PartyActivityOrgYearRecord> KEY_T_PARTY_ACTIVITY_ORG_YEAR_PRIMARY = createUniqueKey(PartyActivityOrgYear.PARTY_ACTIVITY_ORG_YEAR, "KEY_t_party_activity_org_year_PRIMARY", PartyActivityOrgYear.PARTY_ACTIVITY_ORG_YEAR.ID);
        public static final UniqueKey<PartyBusinessConfigurationRecord> KEY_T_PARTY_BUSINESS_CONFIGURATION_PRIMARY = createUniqueKey(PartyBusinessConfiguration.PARTY_BUSINESS_CONFIGURATION, "KEY_t_party_business_configuration_PRIMARY", PartyBusinessConfiguration.PARTY_BUSINESS_CONFIGURATION.ID);
        public static final UniqueKey<PartyDataRecord> KEY_T_PARTY_DATA_PRIMARY = createUniqueKey(PartyData.PARTY_DATA, "KEY_t_party_data_PRIMARY", PartyData.PARTY_DATA.ID);
        public static final UniqueKey<PartyHotTopicRecord> KEY_T_PARTY_HOT_TOPIC_PRIMARY = createUniqueKey(PartyHotTopic.PARTY_HOT_TOPIC, "KEY_t_party_hot_topic_PRIMARY", PartyHotTopic.PARTY_HOT_TOPIC.ID);
        public static final UniqueKey<PartyHotTopicManageRecord> KEY_T_PARTY_HOT_TOPIC_MANAGE_PRIMARY = createUniqueKey(PartyHotTopicManage.PARTY_HOT_TOPIC_MANAGE, "KEY_t_party_hot_topic_manage_PRIMARY", PartyHotTopicManage.PARTY_HOT_TOPIC_MANAGE.ID);
        public static final UniqueKey<PartyLeaderRecord> KEY_T_PARTY_LEADER_PRIMARY = createUniqueKey(PartyLeader.PARTY_LEADER, "KEY_t_party_leader_PRIMARY", PartyLeader.PARTY_LEADER.ID);
        public static final UniqueKey<PartyOrganizationRecord> KEY_T_PARTY_ORGANIZATION_PRIMARY = createUniqueKey(PartyOrganization.PARTY_ORGANIZATION, "KEY_t_party_organization_PRIMARY", PartyOrganization.PARTY_ORGANIZATION.ID);
        public static final UniqueKey<PartyOrganizationRelationshipsRecord> KEY_T_PARTY_ORGANIZATION_RELATIONSHIPS_PRIMARY = createUniqueKey(PartyOrganizationRelationships.PARTY_ORGANIZATION_RELATIONSHIPS, "KEY_t_party_organization_relationships_PRIMARY", PartyOrganizationRelationships.PARTY_ORGANIZATION_RELATIONSHIPS.ID);
        public static final UniqueKey<PartyRecommendationRecord> KEY_T_PARTY_RECOMMENDATION_PRIMARY = createUniqueKey(PartyRecommendation.PARTY_RECOMMENDATION, "KEY_t_party_recommendation_PRIMARY", PartyRecommendation.PARTY_RECOMMENDATION.ID);
        public static final UniqueKey<PartyRecommendResultRecord> KEY_T_PARTY_RECOMMEND_RESULT_PRIMARY = createUniqueKey(PartyRecommendResult.PARTY_RECOMMEND_RESULT, "KEY_t_party_recommend_result_PRIMARY", PartyRecommendResult.PARTY_RECOMMEND_RESULT.ID);
        public static final UniqueKey<PartyRecommendSpareRecord> KEY_T_PARTY_RECOMMEND_SPARE_PRIMARY = createUniqueKey(PartyRecommendSpare.PARTY_RECOMMEND_SPARE, "KEY_t_party_recommend_spare_PRIMARY", PartyRecommendSpare.PARTY_RECOMMEND_SPARE.ID);
        public static final UniqueKey<PartyStudySummaryDayRecord> KEY_T_PARTY_STUDY_SUMMARY_DAY_PRIMARY = createUniqueKey(PartyStudySummaryDay.PARTY_STUDY_SUMMARY_DAY, "KEY_t_party_study_summary_day_PRIMARY", PartyStudySummaryDay.PARTY_STUDY_SUMMARY_DAY.ID);
        public static final UniqueKey<PartyStudySummaryMonthRecord> KEY_T_PARTY_STUDY_SUMMARY_MONTH_PRIMARY = createUniqueKey(PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH, "KEY_t_party_study_summary_month_PRIMARY", PartyStudySummaryMonth.PARTY_STUDY_SUMMARY_MONTH.ID);
        public static final UniqueKey<PartyTopicRecord> KEY_T_PARTY_TOPIC_PRIMARY = createUniqueKey(PartyTopic.PARTY_TOPIC, "KEY_t_party_topic_PRIMARY", PartyTopic.PARTY_TOPIC.ID);
        public static final UniqueKey<PccwOrganizationConfigRecord> KEY_T_PCCW_ORGANIZATION_CONFIG_PRIMARY = createUniqueKey(PccwOrganizationConfig.PCCW_ORGANIZATION_CONFIG, "KEY_t_pccw_organization_config_PRIMARY", PccwOrganizationConfig.PCCW_ORGANIZATION_CONFIG.ID);
        public static final UniqueKey<PccwResultRecord> KEY_T_PCCW_RESULT_PRIMARY = createUniqueKey(PccwResult.PCCW_RESULT, "KEY_t_pccw_result_PRIMARY", PccwResult.PCCW_RESULT.ID);
        public static final UniqueKey<PccwResultBusinessRecord> KEY_T_PCCW_RESULT_BUSINESS_PRIMARY = createUniqueKey(PccwResultBusiness.PCCW_RESULT_BUSINESS, "KEY_t_pccw_result_business_PRIMARY", PccwResultBusiness.PCCW_RESULT_BUSINESS.ID);
        public static final UniqueKey<PccwResultErrorHistoryRecord> KEY_T_PCCW_RESULT_ERROR_HISTORY_PRIMARY = createUniqueKey(PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY, "KEY_t_pccw_result_error_history_PRIMARY", PccwResultErrorHistory.PCCW_RESULT_ERROR_HISTORY.ID);
        public static final UniqueKey<PersonYearBillRecord> KEY_T_PERSON_YEAR_BILL_PRIMARY = createUniqueKey(PersonYearBill.PERSON_YEAR_BILL, "KEY_t_person_year_bill_PRIMARY", PersonYearBill.PERSON_YEAR_BILL.ID);
        public static final UniqueKey<PositionRecord> KEY_T_POSITION_PRIMARY = createUniqueKey(Position.POSITION, "KEY_t_position_PRIMARY", Position.POSITION.ID);
        public static final UniqueKey<PositionOldRecord> KEY_T_POSITION_OLD_PRIMARY = createUniqueKey(PositionOld.POSITION_OLD, "KEY_t_position_old_PRIMARY", PositionOld.POSITION_OLD.ID);
        public static final UniqueKey<QuestionnaireMouldRecord> KEY_T_QUESTIONNAIRE_MOULD_PRIMARY = createUniqueKey(QuestionnaireMould.QUESTIONNAIRE_MOULD, "KEY_t_questionnaire_mould_PRIMARY", QuestionnaireMould.QUESTIONNAIRE_MOULD.ID);
        public static final UniqueKey<QuestionnaireMouldQuestionRecord> KEY_T_QUESTIONNAIRE_MOULD_QUESTION_PRIMARY = createUniqueKey(QuestionnaireMouldQuestion.QUESTIONNAIRE_MOULD_QUESTION, "KEY_t_questionnaire_mould_question_PRIMARY", QuestionnaireMouldQuestion.QUESTIONNAIRE_MOULD_QUESTION.ID);
        public static final UniqueKey<QuestionnaireQuestionRecord> KEY_T_QUESTIONNAIRE_QUESTION_PRIMARY = createUniqueKey(QuestionnaireQuestion.QUESTIONNAIRE_QUESTION, "KEY_t_questionnaire_question_PRIMARY", QuestionnaireQuestion.QUESTIONNAIRE_QUESTION.ID);
        public static final UniqueKey<RemodelingEntryStudyLogRecord> KEY_T_REMODELING_ENTRY_STUDY_LOG_PRIMARY = createUniqueKey(RemodelingEntryStudyLog.REMODELING_ENTRY_STUDY_LOG, "KEY_t_remodeling_entry_study_log_PRIMARY", RemodelingEntryStudyLog.REMODELING_ENTRY_STUDY_LOG.ID);
        public static final UniqueKey<RemodelingEntryStudyProgressRecord> KEY_T_REMODELING_ENTRY_STUDY_PROGRESS_PRIMARY = createUniqueKey(RemodelingEntryStudyProgress.REMODELING_ENTRY_STUDY_PROGRESS, "KEY_t_remodeling_entry_study_progress_PRIMARY", RemodelingEntryStudyProgress.REMODELING_ENTRY_STUDY_PROGRESS.ID);
        public static final UniqueKey<RemodelingEntryStudyProgressRecord> KEY_T_REMODELING_ENTRY_STUDY_PROGRESS_UNIQUE_T_REMODELING_ENTRY_STUDY_PROGRESS_MEMBERIDANDSUBJECTID = createUniqueKey(RemodelingEntryStudyProgress.REMODELING_ENTRY_STUDY_PROGRESS, "KEY_t_remodeling_entry_study_progress_unique_t_remodeling_entry_study_progress_memberIdAndSubjectId", RemodelingEntryStudyProgress.REMODELING_ENTRY_STUDY_PROGRESS.MEMBER_ID, RemodelingEntryStudyProgress.REMODELING_ENTRY_STUDY_PROGRESS.SUBJECT_ID);
        public static final UniqueKey<RemodelingExternalCourseBusinessRecord> KEY_T_REMODELING_EXTERNAL_COURSE_BUSINESS_PRIMARY = createUniqueKey(RemodelingExternalCourseBusiness.REMODELING_EXTERNAL_COURSE_BUSINESS, "KEY_t_remodeling_external_course_business_PRIMARY", RemodelingExternalCourseBusiness.REMODELING_EXTERNAL_COURSE_BUSINESS.ID);
        public static final UniqueKey<RemodelingExternalCourseBusinessRecord> KEY_T_REMODELING_EXTERNAL_COURSE_BUSINESS_UNIQUE_EXTERNAL_COURSE_BUSINESS_EXTERNALCOURSEID_APPID = createUniqueKey(RemodelingExternalCourseBusiness.REMODELING_EXTERNAL_COURSE_BUSINESS, "KEY_t_remodeling_external_course_business_unique_external_course_business_externalCourseId_appId", RemodelingExternalCourseBusiness.REMODELING_EXTERNAL_COURSE_BUSINESS.EXTERNAL_COURSE_ID, RemodelingExternalCourseBusiness.REMODELING_EXTERNAL_COURSE_BUSINESS.APP_ID);
        public static final UniqueKey<RemodelingExternalCourseStudyDetailRecord> KEY_T_REMODELING_EXTERNAL_COURSE_STUDY_DETAIL_PRIMARY = createUniqueKey(RemodelingExternalCourseStudyDetail.REMODELING_EXTERNAL_COURSE_STUDY_DETAIL, "KEY_t_remodeling_external_course_study_detail_PRIMARY", RemodelingExternalCourseStudyDetail.REMODELING_EXTERNAL_COURSE_STUDY_DETAIL.ID);
        public static final UniqueKey<RemodelingExternalExamDetailRecord> KEY_T_REMODELING_EXTERNAL_EXAM_DETAIL_PRIMARY = createUniqueKey(RemodelingExternalExamDetail.REMODELING_EXTERNAL_EXAM_DETAIL, "KEY_t_remodeling_external_exam_detail_PRIMARY", RemodelingExternalExamDetail.REMODELING_EXTERNAL_EXAM_DETAIL.ID);
        public static final UniqueKey<RemodelingExternalPassbackBusinessRecord> KEY_T_REMODELING_EXTERNAL_PASSBACK_BUSINESS_PRIMARY = createUniqueKey(RemodelingExternalPassbackBusiness.REMODELING_EXTERNAL_PASSBACK_BUSINESS, "KEY_t_remodeling_external_passback_business_PRIMARY", RemodelingExternalPassbackBusiness.REMODELING_EXTERNAL_PASSBACK_BUSINESS.ID);
        public static final UniqueKey<RemodelingExternalPassbackBusinessRecord> KEY_T_REMODELING_EXTERNAL_PASSBACK_BUSINESS_UNIQUE_REMODELING_EXTERNAL_PASSBACK_MEMBERIDSECTIONID = createUniqueKey(RemodelingExternalPassbackBusiness.REMODELING_EXTERNAL_PASSBACK_BUSINESS, "KEY_t_remodeling_external_passback_business_unique_remodeling_external_passback_memberIdSectionId", RemodelingExternalPassbackBusiness.REMODELING_EXTERNAL_PASSBACK_BUSINESS.MEMBER_ID, RemodelingExternalPassbackBusiness.REMODELING_EXTERNAL_PASSBACK_BUSINESS.SECTION_ID);
        public static final UniqueKey<RemodelingInternalCourseBusinessRecord> KEY_T_REMODELING_INTERNAL_COURSE_BUSINESS_PRIMARY = createUniqueKey(RemodelingInternalCourseBusiness.REMODELING_INTERNAL_COURSE_BUSINESS, "KEY_t_remodeling_internal_course_business_PRIMARY", RemodelingInternalCourseBusiness.REMODELING_INTERNAL_COURSE_BUSINESS.ID);
        public static final UniqueKey<RemodelingInternalCourseBusinessRecord> KEY_T_REMODELING_INTERNAL_COURSE_BUSINESS_UNIQUE_INTERNAL_COURSE_BUSINESS_COURSEID_REFERENCEID = createUniqueKey(RemodelingInternalCourseBusiness.REMODELING_INTERNAL_COURSE_BUSINESS, "KEY_t_remodeling_internal_course_business_unique_internal_course_business_courseId_referenceId", RemodelingInternalCourseBusiness.REMODELING_INTERNAL_COURSE_BUSINESS.COURSE_ID, RemodelingInternalCourseBusiness.REMODELING_INTERNAL_COURSE_BUSINESS.REFERENCE_ID);
        public static final UniqueKey<RemodelingRoleDetailRecord> KEY_T_REMODELING_ROLE_DETAIL_PRIMARY = createUniqueKey(RemodelingRoleDetail.REMODELING_ROLE_DETAIL, "KEY_t_remodeling_role_detail_PRIMARY", RemodelingRoleDetail.REMODELING_ROLE_DETAIL.ID);
        public static final UniqueKey<RemodelingRoleIssueTimeConfigRecord> KEY_T_REMODELING_ROLE_ISSUE_TIME_CONFIG_PRIMARY = createUniqueKey(RemodelingRoleIssueTimeConfig.REMODELING_ROLE_ISSUE_TIME_CONFIG, "KEY_t_remodeling_role_issue_time_config_PRIMARY", RemodelingRoleIssueTimeConfig.REMODELING_ROLE_ISSUE_TIME_CONFIG.ID);
        public static final UniqueKey<RepeatCourseSectionStudyProgressRecord> KEY_T_REPEAT_COURSE_SECTION_STUDY_PROGRESS_PRIMARY = createUniqueKey(RepeatCourseSectionStudyProgress.REPEAT_COURSE_SECTION_STUDY_PROGRESS, "KEY_t_repeat_course_section_study_progress_PRIMARY", RepeatCourseSectionStudyProgress.REPEAT_COURSE_SECTION_STUDY_PROGRESS.ID);
        public static final UniqueKey<ShardingConfigRecord> KEY_T_SHARDING_CONFIG_PRIMARY = createUniqueKey(ShardingConfig.SHARDING_CONFIG, "KEY_t_sharding_config_PRIMARY", ShardingConfig.SHARDING_CONFIG.ID);
        public static final UniqueKey<SplitLogConfigRecord> KEY_T_SPLIT_LOG_CONFIG_PRIMARY = createUniqueKey(SplitLogConfig.SPLIT_LOG_CONFIG, "KEY_t_split_log_config_PRIMARY", SplitLogConfig.SPLIT_LOG_CONFIG.ID);
        public static final UniqueKey<SplitLogTimeRecord> KEY_T_SPLIT_LOG_TIME_PRIMARY = createUniqueKey(SplitLogTime.SPLIT_LOG_TIME, "KEY_t_split_log_time_PRIMARY", SplitLogTime.SPLIT_LOG_TIME.ID);
        public static final UniqueKey<SplitTableConfigRecord> KEY_T_SPLIT_TABLE_CONFIG_PRIMARY = createUniqueKey(SplitTableConfig.SPLIT_TABLE_CONFIG, "KEY_t_split_table_config_PRIMARY", SplitTableConfig.SPLIT_TABLE_CONFIG.ID);
        public static final UniqueKey<SplitTableCountRecord> KEY_T_SPLIT_TABLE_COUNT_PRIMARY = createUniqueKey(SplitTableCount.SPLIT_TABLE_COUNT, "KEY_t_split_table_count_PRIMARY", SplitTableCount.SPLIT_TABLE_COUNT.ID);
        public static final UniqueKey<SplitTableTimeRecord> KEY_T_SPLIT_TABLE_TIME_PRIMARY = createUniqueKey(SplitTableTime.SPLIT_TABLE_TIME, "KEY_t_split_table_time_PRIMARY", SplitTableTime.SPLIT_TABLE_TIME.ID);
        public static final UniqueKey<StudyActivityConfigRecord> KEY_T_STUDY_ACTIVITY_CONFIG_PRIMARY = createUniqueKey(StudyActivityConfig.STUDY_ACTIVITY_CONFIG, "KEY_t_study_activity_config_PRIMARY", StudyActivityConfig.STUDY_ACTIVITY_CONFIG.ID);
        public static final UniqueKey<StudyExperienceRecord> KEY_T_STUDY_EXPERIENCE_PRIMARY = createUniqueKey(StudyExperience.STUDY_EXPERIENCE, "KEY_t_study_experience_PRIMARY", StudyExperience.STUDY_EXPERIENCE.ID);
        public static final UniqueKey<StudyPushAudienceObjectRecord> KEY_T_STUDY_PUSH_AUDIENCE_OBJECT_PRIMARY = createUniqueKey(StudyPushAudienceObject.STUDY_PUSH_AUDIENCE_OBJECT, "KEY_t_study_push_audience_object_PRIMARY", StudyPushAudienceObject.STUDY_PUSH_AUDIENCE_OBJECT.ID);
        public static final UniqueKey<StudyPushInfoRecord> KEY_T_STUDY_PUSH_INFO_PRIMARY = createUniqueKey(StudyPushInfo.STUDY_PUSH_INFO, "KEY_t_study_push_info_PRIMARY", StudyPushInfo.STUDY_PUSH_INFO.ID);
        public static final UniqueKey<StudyPushObjectRecord> KEY_T_STUDY_PUSH_OBJECT_PRIMARY = createUniqueKey(StudyPushObject.STUDY_PUSH_OBJECT, "KEY_t_study_push_object_PRIMARY", StudyPushObject.STUDY_PUSH_OBJECT.ID);
        public static final UniqueKey<StudyPushRecordRecord> KEY_T_STUDY_PUSH_RECORD_PRIMARY = createUniqueKey(StudyPushRecord.STUDY_PUSH_RECORD, "KEY_t_study_push_record_PRIMARY", StudyPushRecord.STUDY_PUSH_RECORD.ID);
        public static final UniqueKey<StudyPushShelvesRecord> KEY_T_STUDY_PUSH_SHELVES_PRIMARY = createUniqueKey(StudyPushShelves.STUDY_PUSH_SHELVES, "KEY_t_study_push_shelves_PRIMARY", StudyPushShelves.STUDY_PUSH_SHELVES.ID);
        public static final UniqueKey<StudyRecord_2017Record> KEY_T_STUDY_RECORD_2017_PRIMARY = createUniqueKey(StudyRecord_2017.STUDY_RECORD_2017, "KEY_t_study_record_2017_PRIMARY", StudyRecord_2017.STUDY_RECORD_2017.CID);
        public static final UniqueKey<StudyTaskRecord> KEY_T_STUDY_TASK_PRIMARY = createUniqueKey(StudyTask.STUDY_TASK, "KEY_t_study_task_PRIMARY", StudyTask.STUDY_TASK.ID);
        public static final UniqueKey<StudyTaskAttachmentRecord> KEY_T_STUDY_TASK_ATTACHMENT_PRIMARY = createUniqueKey(StudyTaskAttachment.STUDY_TASK_ATTACHMENT, "KEY_t_study_task_attachment_PRIMARY", StudyTaskAttachment.STUDY_TASK_ATTACHMENT.ID);
        public static final UniqueKey<StudyTaskAuditMemberRecord> KEY_T_STUDY_TASK_AUDIT_MEMBER_PRIMARY = createUniqueKey(StudyTaskAuditMember.STUDY_TASK_AUDIT_MEMBER, "KEY_t_study_task_audit_member_PRIMARY", StudyTaskAuditMember.STUDY_TASK_AUDIT_MEMBER.ID);
        public static final UniqueKey<SubjectAdvertisingRecord> KEY_T_SUBJECT_ADVERTISING_PRIMARY = createUniqueKey(SubjectAdvertising.SUBJECT_ADVERTISING, "KEY_t_subject_advertising_PRIMARY", SubjectAdvertising.SUBJECT_ADVERTISING.ID);
        public static final UniqueKey<SubjectCourseRecord> KEY_T_SUBJECT_COURSE_PRIMARY = createUniqueKey(SubjectCourse.SUBJECT_COURSE, "KEY_t_subject_course_PRIMARY", SubjectCourse.SUBJECT_COURSE.ID);
        public static final UniqueKey<SubjectDirectionRecord> KEY_T_SUBJECT_DIRECTION_PRIMARY = createUniqueKey(SubjectDirection.SUBJECT_DIRECTION, "KEY_t_subject_direction_PRIMARY", SubjectDirection.SUBJECT_DIRECTION.ID);
        public static final UniqueKey<SubjectMemberBlacklistRecord> KEY_T_SUBJECT_MEMBER_BLACKLIST_PRIMARY = createUniqueKey(SubjectMemberBlacklist.SUBJECT_MEMBER_BLACKLIST, "KEY_t_subject_member_blacklist_PRIMARY", SubjectMemberBlacklist.SUBJECT_MEMBER_BLACKLIST.ID);
        public static final UniqueKey<SubjectProblemRecord> KEY_T_SUBJECT_PROBLEM_PRIMARY = createUniqueKey(SubjectProblem.SUBJECT_PROBLEM, "KEY_t_subject_problem_PRIMARY", SubjectProblem.SUBJECT_PROBLEM.ID);
        public static final UniqueKey<SubjectRankRecord> KEY_T_SUBJECT_RANK_PRIMARY = createUniqueKey(SubjectRank.SUBJECT_RANK, "KEY_t_subject_rank_PRIMARY", SubjectRank.SUBJECT_RANK.ID);
        public static final UniqueKey<SubjectRecommendRecord> KEY_T_SUBJECT_RECOMMEND_PRIMARY = createUniqueKey(SubjectRecommend.SUBJECT_RECOMMEND, "KEY_t_subject_recommend_PRIMARY", SubjectRecommend.SUBJECT_RECOMMEND.ID);
        public static final UniqueKey<SubjectRoleCommentRecord> KEY_T_SUBJECT_ROLE_COMMENT_PRIMARY = createUniqueKey(SubjectRoleComment.SUBJECT_ROLE_COMMENT, "KEY_t_subject_role_comment_PRIMARY", SubjectRoleComment.SUBJECT_ROLE_COMMENT.ID);
        public static final UniqueKey<SubjectRoleDetailRecord> KEY_T_SUBJECT_ROLE_DETAIL_PRIMARY = createUniqueKey(SubjectRoleDetail.SUBJECT_ROLE_DETAIL, "KEY_t_subject_role_detail_PRIMARY", SubjectRoleDetail.SUBJECT_ROLE_DETAIL.ID);
        public static final UniqueKey<SubjectSectionStudyLogRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_PRIMARY = createUniqueKey(SubjectSectionStudyLog.SUBJECT_SECTION_STUDY_LOG, "KEY_t_subject_section_study_log_PRIMARY", SubjectSectionStudyLog.SUBJECT_SECTION_STUDY_LOG.ID);
        public static final UniqueKey<SubjectSectionStudyLogAhRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_AH_PRIMARY = createUniqueKey(SubjectSectionStudyLogAh.SUBJECT_SECTION_STUDY_LOG_AH, "KEY_t_subject_section_study_log_ah_PRIMARY", SubjectSectionStudyLogAh.SUBJECT_SECTION_STUDY_LOG_AH.ID);
        public static final UniqueKey<SubjectSectionStudyLogBjRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_BJ_PRIMARY = createUniqueKey(SubjectSectionStudyLogBj.SUBJECT_SECTION_STUDY_LOG_BJ, "KEY_t_subject_section_study_log_bj_PRIMARY", SubjectSectionStudyLogBj.SUBJECT_SECTION_STUDY_LOG_BJ.ID);
        public static final UniqueKey<SubjectSectionStudyLogCmRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_CM_PRIMARY = createUniqueKey(SubjectSectionStudyLogCm.SUBJECT_SECTION_STUDY_LOG_CM, "KEY_t_subject_section_study_log_cm_PRIMARY", SubjectSectionStudyLogCm.SUBJECT_SECTION_STUDY_LOG_CM.ID);
        public static final UniqueKey<SubjectSectionStudyLogCqRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_CQ_PRIMARY = createUniqueKey(SubjectSectionStudyLogCq.SUBJECT_SECTION_STUDY_LOG_CQ, "KEY_t_subject_section_study_log_cq_PRIMARY", SubjectSectionStudyLogCq.SUBJECT_SECTION_STUDY_LOG_CQ.ID);
        public static final UniqueKey<SubjectSectionStudyLogEbRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_EB_PRIMARY = createUniqueKey(SubjectSectionStudyLogEb.SUBJECT_SECTION_STUDY_LOG_EB, "KEY_t_subject_section_study_log_eb_PRIMARY", SubjectSectionStudyLogEb.SUBJECT_SECTION_STUDY_LOG_EB.ID);
        public static final UniqueKey<SubjectSectionStudyLogFjRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_FJ_PRIMARY = createUniqueKey(SubjectSectionStudyLogFj.SUBJECT_SECTION_STUDY_LOG_FJ, "KEY_t_subject_section_study_log_fj_PRIMARY", SubjectSectionStudyLogFj.SUBJECT_SECTION_STUDY_LOG_FJ.ID);
        public static final UniqueKey<SubjectSectionStudyLogGdRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_GD_PRIMARY = createUniqueKey(SubjectSectionStudyLogGd.SUBJECT_SECTION_STUDY_LOG_GD, "KEY_t_subject_section_study_log_gd_PRIMARY", SubjectSectionStudyLogGd.SUBJECT_SECTION_STUDY_LOG_GD.ID);
        public static final UniqueKey<SubjectSectionStudyLogGsRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_GS_PRIMARY = createUniqueKey(SubjectSectionStudyLogGs.SUBJECT_SECTION_STUDY_LOG_GS, "KEY_t_subject_section_study_log_gs_PRIMARY", SubjectSectionStudyLogGs.SUBJECT_SECTION_STUDY_LOG_GS.ID);
        public static final UniqueKey<SubjectSectionStudyLogGxRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_GX_PRIMARY = createUniqueKey(SubjectSectionStudyLogGx.SUBJECT_SECTION_STUDY_LOG_GX, "KEY_t_subject_section_study_log_gx_PRIMARY", SubjectSectionStudyLogGx.SUBJECT_SECTION_STUDY_LOG_GX.ID);
        public static final UniqueKey<SubjectSectionStudyLogGzRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_GZ_PRIMARY = createUniqueKey(SubjectSectionStudyLogGz.SUBJECT_SECTION_STUDY_LOG_GZ, "KEY_t_subject_section_study_log_gz_PRIMARY", SubjectSectionStudyLogGz.SUBJECT_SECTION_STUDY_LOG_GZ.ID);
        public static final UniqueKey<SubjectSectionStudyLogHbRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_HB_PRIMARY = createUniqueKey(SubjectSectionStudyLogHb.SUBJECT_SECTION_STUDY_LOG_HB, "KEY_t_subject_section_study_log_hb_PRIMARY", SubjectSectionStudyLogHb.SUBJECT_SECTION_STUDY_LOG_HB.ID);
        public static final UniqueKey<SubjectSectionStudyLogHlRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_HL_PRIMARY = createUniqueKey(SubjectSectionStudyLogHl.SUBJECT_SECTION_STUDY_LOG_HL, "KEY_t_subject_section_study_log_hl_PRIMARY", SubjectSectionStudyLogHl.SUBJECT_SECTION_STUDY_LOG_HL.ID);
        public static final UniqueKey<SubjectSectionStudyLogHnRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_HN_PRIMARY = createUniqueKey(SubjectSectionStudyLogHn.SUBJECT_SECTION_STUDY_LOG_HN, "KEY_t_subject_section_study_log_hn_PRIMARY", SubjectSectionStudyLogHn.SUBJECT_SECTION_STUDY_LOG_HN.ID);
        public static final UniqueKey<SubjectSectionStudyLogJlRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_JL_PRIMARY = createUniqueKey(SubjectSectionStudyLogJl.SUBJECT_SECTION_STUDY_LOG_JL, "KEY_t_subject_section_study_log_jl_PRIMARY", SubjectSectionStudyLogJl.SUBJECT_SECTION_STUDY_LOG_JL.ID);
        public static final UniqueKey<SubjectSectionStudyLogJsRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_JS_PRIMARY = createUniqueKey(SubjectSectionStudyLogJs.SUBJECT_SECTION_STUDY_LOG_JS, "KEY_t_subject_section_study_log_js_PRIMARY", SubjectSectionStudyLogJs.SUBJECT_SECTION_STUDY_LOG_JS.ID);
        public static final UniqueKey<SubjectSectionStudyLogJxRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_JX_PRIMARY = createUniqueKey(SubjectSectionStudyLogJx.SUBJECT_SECTION_STUDY_LOG_JX, "KEY_t_subject_section_study_log_jx_PRIMARY", SubjectSectionStudyLogJx.SUBJECT_SECTION_STUDY_LOG_JX.ID);
        public static final UniqueKey<SubjectSectionStudyLogLnRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_LN_PRIMARY = createUniqueKey(SubjectSectionStudyLogLn.SUBJECT_SECTION_STUDY_LOG_LN, "KEY_t_subject_section_study_log_ln_PRIMARY", SubjectSectionStudyLogLn.SUBJECT_SECTION_STUDY_LOG_LN.ID);
        public static final UniqueKey<SubjectSectionStudyLogNmRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_NM_PRIMARY = createUniqueKey(SubjectSectionStudyLogNm.SUBJECT_SECTION_STUDY_LOG_NM, "KEY_t_subject_section_study_log_nm_PRIMARY", SubjectSectionStudyLogNm.SUBJECT_SECTION_STUDY_LOG_NM.ID);
        public static final UniqueKey<SubjectSectionStudyLogNxRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_NX_PRIMARY = createUniqueKey(SubjectSectionStudyLogNx.SUBJECT_SECTION_STUDY_LOG_NX, "KEY_t_subject_section_study_log_nx_PRIMARY", SubjectSectionStudyLogNx.SUBJECT_SECTION_STUDY_LOG_NX.ID);
        public static final UniqueKey<SubjectSectionStudyLogOtherRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_OTHER_PRIMARY = createUniqueKey(SubjectSectionStudyLogOther.SUBJECT_SECTION_STUDY_LOG_OTHER, "KEY_t_subject_section_study_log_other_PRIMARY", SubjectSectionStudyLogOther.SUBJECT_SECTION_STUDY_LOG_OTHER.ID);
        public static final UniqueKey<SubjectSectionStudyLogQhRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_QH_PRIMARY = createUniqueKey(SubjectSectionStudyLogQh.SUBJECT_SECTION_STUDY_LOG_QH, "KEY_t_subject_section_study_log_qh_PRIMARY", SubjectSectionStudyLogQh.SUBJECT_SECTION_STUDY_LOG_QH.ID);
        public static final UniqueKey<SubjectSectionStudyLogQoRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_QO_PRIMARY = createUniqueKey(SubjectSectionStudyLogQo.SUBJECT_SECTION_STUDY_LOG_QO, "KEY_t_subject_section_study_log_qo_PRIMARY", SubjectSectionStudyLogQo.SUBJECT_SECTION_STUDY_LOG_QO.ID);
        public static final UniqueKey<SubjectSectionStudyLogScRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_SC_PRIMARY = createUniqueKey(SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC, "KEY_t_subject_section_study_log_sc_PRIMARY", SubjectSectionStudyLogSc.SUBJECT_SECTION_STUDY_LOG_SC.ID);
        public static final UniqueKey<SubjectSectionStudyLogSdRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_SD_PRIMARY = createUniqueKey(SubjectSectionStudyLogSd.SUBJECT_SECTION_STUDY_LOG_SD, "KEY_t_subject_section_study_log_sd_PRIMARY", SubjectSectionStudyLogSd.SUBJECT_SECTION_STUDY_LOG_SD.ID);
        public static final UniqueKey<SubjectSectionStudyLogShRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_SH_PRIMARY = createUniqueKey(SubjectSectionStudyLogSh.SUBJECT_SECTION_STUDY_LOG_SH, "KEY_t_subject_section_study_log_sh_PRIMARY", SubjectSectionStudyLogSh.SUBJECT_SECTION_STUDY_LOG_SH.ID);
        public static final UniqueKey<SubjectSectionStudyLogSnRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_SN_PRIMARY = createUniqueKey(SubjectSectionStudyLogSn.SUBJECT_SECTION_STUDY_LOG_SN, "KEY_t_subject_section_study_log_sn_PRIMARY", SubjectSectionStudyLogSn.SUBJECT_SECTION_STUDY_LOG_SN.ID);
        public static final UniqueKey<SubjectSectionStudyLogSxRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_SX_PRIMARY = createUniqueKey(SubjectSectionStudyLogSx.SUBJECT_SECTION_STUDY_LOG_SX, "KEY_t_subject_section_study_log_sx_PRIMARY", SubjectSectionStudyLogSx.SUBJECT_SECTION_STUDY_LOG_SX.ID);
        public static final UniqueKey<SubjectSectionStudyLogTjRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_TJ_PRIMARY = createUniqueKey(SubjectSectionStudyLogTj.SUBJECT_SECTION_STUDY_LOG_TJ, "KEY_t_subject_section_study_log_tj_PRIMARY", SubjectSectionStudyLogTj.SUBJECT_SECTION_STUDY_LOG_TJ.ID);
        public static final UniqueKey<SubjectSectionStudyLogXjRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_XJ_PRIMARY = createUniqueKey(SubjectSectionStudyLogXj.SUBJECT_SECTION_STUDY_LOG_XJ, "KEY_t_subject_section_study_log_xj_PRIMARY", SubjectSectionStudyLogXj.SUBJECT_SECTION_STUDY_LOG_XJ.ID);
        public static final UniqueKey<SubjectSectionStudyLogXnRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_XN_PRIMARY = createUniqueKey(SubjectSectionStudyLogXn.SUBJECT_SECTION_STUDY_LOG_XN, "KEY_t_subject_section_study_log_xn_PRIMARY", SubjectSectionStudyLogXn.SUBJECT_SECTION_STUDY_LOG_XN.ID);
        public static final UniqueKey<SubjectSectionStudyLogXzRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_XZ_PRIMARY = createUniqueKey(SubjectSectionStudyLogXz.SUBJECT_SECTION_STUDY_LOG_XZ, "KEY_t_subject_section_study_log_xz_PRIMARY", SubjectSectionStudyLogXz.SUBJECT_SECTION_STUDY_LOG_XZ.ID);
        public static final UniqueKey<SubjectSectionStudyLogYnRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_YN_PRIMARY = createUniqueKey(SubjectSectionStudyLogYn.SUBJECT_SECTION_STUDY_LOG_YN, "KEY_t_subject_section_study_log_yn_PRIMARY", SubjectSectionStudyLogYn.SUBJECT_SECTION_STUDY_LOG_YN.ID);
        public static final UniqueKey<SubjectSectionStudyLogZgttRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_ZGTT_PRIMARY = createUniqueKey(SubjectSectionStudyLogZgtt.SUBJECT_SECTION_STUDY_LOG_ZGTT, "KEY_t_subject_section_study_log_zgtt_PRIMARY", SubjectSectionStudyLogZgtt.SUBJECT_SECTION_STUDY_LOG_ZGTT.ID);
        public static final UniqueKey<SubjectSectionStudyLogZjRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_ZJ_PRIMARY = createUniqueKey(SubjectSectionStudyLogZj.SUBJECT_SECTION_STUDY_LOG_ZJ, "KEY_t_subject_section_study_log_zj_PRIMARY", SubjectSectionStudyLogZj.SUBJECT_SECTION_STUDY_LOG_ZJ.ID);
        public static final UniqueKey<SubjectSectionStudyLogZxRecord> KEY_T_SUBJECT_SECTION_STUDY_LOG_ZX_PRIMARY = createUniqueKey(SubjectSectionStudyLogZx.SUBJECT_SECTION_STUDY_LOG_ZX, "KEY_t_subject_section_study_log_zx_PRIMARY", SubjectSectionStudyLogZx.SUBJECT_SECTION_STUDY_LOG_ZX.ID);
        public static final UniqueKey<SubjectStudyDayExceptionRecord> KEY_T_SUBJECT_STUDY_DAY_EXCEPTION_PRIMARY = createUniqueKey(SubjectStudyDayException.SUBJECT_STUDY_DAY_EXCEPTION, "KEY_t_subject_study_day_exception_PRIMARY", SubjectStudyDayException.SUBJECT_STUDY_DAY_EXCEPTION.ID);
        public static final UniqueKey<SubjectStudyLogAhDayRecord> KEY_T_SUBJECT_STUDY_LOG_AH_DAY_PRIMARY = createUniqueKey(SubjectStudyLogAhDay.SUBJECT_STUDY_LOG_AH_DAY, "KEY_t_subject_study_log_ah_day_PRIMARY", SubjectStudyLogAhDay.SUBJECT_STUDY_LOG_AH_DAY.ID);
        public static final UniqueKey<SubjectStudyLogBjDayRecord> KEY_T_SUBJECT_STUDY_LOG_BJ_DAY_PRIMARY = createUniqueKey(SubjectStudyLogBjDay.SUBJECT_STUDY_LOG_BJ_DAY, "KEY_t_subject_study_log_bj_day_PRIMARY", SubjectStudyLogBjDay.SUBJECT_STUDY_LOG_BJ_DAY.ID);
        public static final UniqueKey<SubjectStudyLogCmDayRecord> KEY_T_SUBJECT_STUDY_LOG_CM_DAY_PRIMARY = createUniqueKey(SubjectStudyLogCmDay.SUBJECT_STUDY_LOG_CM_DAY, "KEY_t_subject_study_log_cm_day_PRIMARY", SubjectStudyLogCmDay.SUBJECT_STUDY_LOG_CM_DAY.ID);
        public static final UniqueKey<SubjectStudyLogCqDayRecord> KEY_T_SUBJECT_STUDY_LOG_CQ_DAY_PRIMARY = createUniqueKey(SubjectStudyLogCqDay.SUBJECT_STUDY_LOG_CQ_DAY, "KEY_t_subject_study_log_cq_day_PRIMARY", SubjectStudyLogCqDay.SUBJECT_STUDY_LOG_CQ_DAY.ID);
        public static final UniqueKey<SubjectStudyLogEbDayRecord> KEY_T_SUBJECT_STUDY_LOG_EB_DAY_PRIMARY = createUniqueKey(SubjectStudyLogEbDay.SUBJECT_STUDY_LOG_EB_DAY, "KEY_t_subject_study_log_eb_day_PRIMARY", SubjectStudyLogEbDay.SUBJECT_STUDY_LOG_EB_DAY.ID);
        public static final UniqueKey<SubjectStudyLogExceptionRecord> KEY_T_SUBJECT_STUDY_LOG_EXCEPTION_PRIMARY = createUniqueKey(SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION, "KEY_t_subject_study_log_exception_PRIMARY", SubjectStudyLogException.SUBJECT_STUDY_LOG_EXCEPTION.ID);
        public static final UniqueKey<SubjectStudyLogFjDayRecord> KEY_T_SUBJECT_STUDY_LOG_FJ_DAY_PRIMARY = createUniqueKey(SubjectStudyLogFjDay.SUBJECT_STUDY_LOG_FJ_DAY, "KEY_t_subject_study_log_fj_day_PRIMARY", SubjectStudyLogFjDay.SUBJECT_STUDY_LOG_FJ_DAY.ID);
        public static final UniqueKey<SubjectStudyLogGdDayRecord> KEY_T_SUBJECT_STUDY_LOG_GD_DAY_PRIMARY = createUniqueKey(SubjectStudyLogGdDay.SUBJECT_STUDY_LOG_GD_DAY, "KEY_t_subject_study_log_gd_day_PRIMARY", SubjectStudyLogGdDay.SUBJECT_STUDY_LOG_GD_DAY.ID);
        public static final UniqueKey<SubjectStudyLogGsDayRecord> KEY_T_SUBJECT_STUDY_LOG_GS_DAY_PRIMARY = createUniqueKey(SubjectStudyLogGsDay.SUBJECT_STUDY_LOG_GS_DAY, "KEY_t_subject_study_log_gs_day_PRIMARY", SubjectStudyLogGsDay.SUBJECT_STUDY_LOG_GS_DAY.ID);
        public static final UniqueKey<SubjectStudyLogGxDayRecord> KEY_T_SUBJECT_STUDY_LOG_GX_DAY_PRIMARY = createUniqueKey(SubjectStudyLogGxDay.SUBJECT_STUDY_LOG_GX_DAY, "KEY_t_subject_study_log_gx_day_PRIMARY", SubjectStudyLogGxDay.SUBJECT_STUDY_LOG_GX_DAY.ID);
        public static final UniqueKey<SubjectStudyLogGzDayRecord> KEY_T_SUBJECT_STUDY_LOG_GZ_DAY_PRIMARY = createUniqueKey(SubjectStudyLogGzDay.SUBJECT_STUDY_LOG_GZ_DAY, "KEY_t_subject_study_log_gz_day_PRIMARY", SubjectStudyLogGzDay.SUBJECT_STUDY_LOG_GZ_DAY.ID);
        public static final UniqueKey<SubjectStudyLogHbDayRecord> KEY_T_SUBJECT_STUDY_LOG_HB_DAY_PRIMARY = createUniqueKey(SubjectStudyLogHbDay.SUBJECT_STUDY_LOG_HB_DAY, "KEY_t_subject_study_log_hb_day_PRIMARY", SubjectStudyLogHbDay.SUBJECT_STUDY_LOG_HB_DAY.ID);
        public static final UniqueKey<SubjectStudyLogHlDayRecord> KEY_T_SUBJECT_STUDY_LOG_HL_DAY_PRIMARY = createUniqueKey(SubjectStudyLogHlDay.SUBJECT_STUDY_LOG_HL_DAY, "KEY_t_subject_study_log_hl_day_PRIMARY", SubjectStudyLogHlDay.SUBJECT_STUDY_LOG_HL_DAY.ID);
        public static final UniqueKey<SubjectStudyLogHnDayRecord> KEY_T_SUBJECT_STUDY_LOG_HN_DAY_PRIMARY = createUniqueKey(SubjectStudyLogHnDay.SUBJECT_STUDY_LOG_HN_DAY, "KEY_t_subject_study_log_hn_day_PRIMARY", SubjectStudyLogHnDay.SUBJECT_STUDY_LOG_HN_DAY.ID);
        public static final UniqueKey<SubjectStudyLogJlDayRecord> KEY_T_SUBJECT_STUDY_LOG_JL_DAY_PRIMARY = createUniqueKey(SubjectStudyLogJlDay.SUBJECT_STUDY_LOG_JL_DAY, "KEY_t_subject_study_log_jl_day_PRIMARY", SubjectStudyLogJlDay.SUBJECT_STUDY_LOG_JL_DAY.ID);
        public static final UniqueKey<SubjectStudyLogJsDayRecord> KEY_T_SUBJECT_STUDY_LOG_JS_DAY_PRIMARY = createUniqueKey(SubjectStudyLogJsDay.SUBJECT_STUDY_LOG_JS_DAY, "KEY_t_subject_study_log_js_day_PRIMARY", SubjectStudyLogJsDay.SUBJECT_STUDY_LOG_JS_DAY.ID);
        public static final UniqueKey<SubjectStudyLogJxDayRecord> KEY_T_SUBJECT_STUDY_LOG_JX_DAY_PRIMARY = createUniqueKey(SubjectStudyLogJxDay.SUBJECT_STUDY_LOG_JX_DAY, "KEY_t_subject_study_log_jx_day_PRIMARY", SubjectStudyLogJxDay.SUBJECT_STUDY_LOG_JX_DAY.ID);
        public static final UniqueKey<SubjectStudyLogLnDayRecord> KEY_T_SUBJECT_STUDY_LOG_LN_DAY_PRIMARY = createUniqueKey(SubjectStudyLogLnDay.SUBJECT_STUDY_LOG_LN_DAY, "KEY_t_subject_study_log_ln_day_PRIMARY", SubjectStudyLogLnDay.SUBJECT_STUDY_LOG_LN_DAY.ID);
        public static final UniqueKey<SubjectStudyLogNmDayRecord> KEY_T_SUBJECT_STUDY_LOG_NM_DAY_PRIMARY = createUniqueKey(SubjectStudyLogNmDay.SUBJECT_STUDY_LOG_NM_DAY, "KEY_t_subject_study_log_nm_day_PRIMARY", SubjectStudyLogNmDay.SUBJECT_STUDY_LOG_NM_DAY.ID);
        public static final UniqueKey<SubjectStudyLogNxDayRecord> KEY_T_SUBJECT_STUDY_LOG_NX_DAY_PRIMARY = createUniqueKey(SubjectStudyLogNxDay.SUBJECT_STUDY_LOG_NX_DAY, "KEY_t_subject_study_log_nx_day_PRIMARY", SubjectStudyLogNxDay.SUBJECT_STUDY_LOG_NX_DAY.ID);
        public static final UniqueKey<SubjectStudyLogOtherDayRecord> KEY_T_SUBJECT_STUDY_LOG_OTHER_DAY_PRIMARY = createUniqueKey(SubjectStudyLogOtherDay.SUBJECT_STUDY_LOG_OTHER_DAY, "KEY_t_subject_study_log_other_day_PRIMARY", SubjectStudyLogOtherDay.SUBJECT_STUDY_LOG_OTHER_DAY.ID);
        public static final UniqueKey<SubjectStudyLogQhDayRecord> KEY_T_SUBJECT_STUDY_LOG_QH_DAY_PRIMARY = createUniqueKey(SubjectStudyLogQhDay.SUBJECT_STUDY_LOG_QH_DAY, "KEY_t_subject_study_log_qh_day_PRIMARY", SubjectStudyLogQhDay.SUBJECT_STUDY_LOG_QH_DAY.ID);
        public static final UniqueKey<SubjectStudyLogQoDayRecord> KEY_T_SUBJECT_STUDY_LOG_QO_DAY_PRIMARY = createUniqueKey(SubjectStudyLogQoDay.SUBJECT_STUDY_LOG_QO_DAY, "KEY_t_subject_study_log_qo_day_PRIMARY", SubjectStudyLogQoDay.SUBJECT_STUDY_LOG_QO_DAY.ID);
        public static final UniqueKey<SubjectStudyLogScDayRecord> KEY_T_SUBJECT_STUDY_LOG_SC_DAY_PRIMARY = createUniqueKey(SubjectStudyLogScDay.SUBJECT_STUDY_LOG_SC_DAY, "KEY_t_subject_study_log_sc_day_PRIMARY", SubjectStudyLogScDay.SUBJECT_STUDY_LOG_SC_DAY.ID);
        public static final UniqueKey<SubjectStudyLogSdDayRecord> KEY_T_SUBJECT_STUDY_LOG_SD_DAY_PRIMARY = createUniqueKey(SubjectStudyLogSdDay.SUBJECT_STUDY_LOG_SD_DAY, "KEY_t_subject_study_log_sd_day_PRIMARY", SubjectStudyLogSdDay.SUBJECT_STUDY_LOG_SD_DAY.ID);
        public static final UniqueKey<SubjectStudyLogShDayRecord> KEY_T_SUBJECT_STUDY_LOG_SH_DAY_PRIMARY = createUniqueKey(SubjectStudyLogShDay.SUBJECT_STUDY_LOG_SH_DAY, "KEY_t_subject_study_log_sh_day_PRIMARY", SubjectStudyLogShDay.SUBJECT_STUDY_LOG_SH_DAY.ID);
        public static final UniqueKey<SubjectStudyLogSnDayRecord> KEY_T_SUBJECT_STUDY_LOG_SN_DAY_PRIMARY = createUniqueKey(SubjectStudyLogSnDay.SUBJECT_STUDY_LOG_SN_DAY, "KEY_t_subject_study_log_sn_day_PRIMARY", SubjectStudyLogSnDay.SUBJECT_STUDY_LOG_SN_DAY.ID);
        public static final UniqueKey<SubjectStudyLogSxDayRecord> KEY_T_SUBJECT_STUDY_LOG_SX_DAY_PRIMARY = createUniqueKey(SubjectStudyLogSxDay.SUBJECT_STUDY_LOG_SX_DAY, "KEY_t_subject_study_log_sx_day_PRIMARY", SubjectStudyLogSxDay.SUBJECT_STUDY_LOG_SX_DAY.ID);
        public static final UniqueKey<SubjectStudyLogTjDayRecord> KEY_T_SUBJECT_STUDY_LOG_TJ_DAY_PRIMARY = createUniqueKey(SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY, "KEY_t_subject_study_log_tj_day_PRIMARY", SubjectStudyLogTjDay.SUBJECT_STUDY_LOG_TJ_DAY.ID);
        public static final UniqueKey<SubjectStudyLogXjDayRecord> KEY_T_SUBJECT_STUDY_LOG_XJ_DAY_PRIMARY = createUniqueKey(SubjectStudyLogXjDay.SUBJECT_STUDY_LOG_XJ_DAY, "KEY_t_subject_study_log_xj_day_PRIMARY", SubjectStudyLogXjDay.SUBJECT_STUDY_LOG_XJ_DAY.ID);
        public static final UniqueKey<SubjectStudyLogXnDayRecord> KEY_T_SUBJECT_STUDY_LOG_XN_DAY_PRIMARY = createUniqueKey(SubjectStudyLogXnDay.SUBJECT_STUDY_LOG_XN_DAY, "KEY_t_subject_study_log_xn_day_PRIMARY", SubjectStudyLogXnDay.SUBJECT_STUDY_LOG_XN_DAY.ID);
        public static final UniqueKey<SubjectStudyLogXzDayRecord> KEY_T_SUBJECT_STUDY_LOG_XZ_DAY_PRIMARY = createUniqueKey(SubjectStudyLogXzDay.SUBJECT_STUDY_LOG_XZ_DAY, "KEY_t_subject_study_log_xz_day_PRIMARY", SubjectStudyLogXzDay.SUBJECT_STUDY_LOG_XZ_DAY.ID);
        public static final UniqueKey<SubjectStudyLogYnDayRecord> KEY_T_SUBJECT_STUDY_LOG_YN_DAY_PRIMARY = createUniqueKey(SubjectStudyLogYnDay.SUBJECT_STUDY_LOG_YN_DAY, "KEY_t_subject_study_log_yn_day_PRIMARY", SubjectStudyLogYnDay.SUBJECT_STUDY_LOG_YN_DAY.ID);
        public static final UniqueKey<SubjectStudyLogZgttDayRecord> KEY_T_SUBJECT_STUDY_LOG_ZGTT_DAY_PRIMARY = createUniqueKey(SubjectStudyLogZgttDay.SUBJECT_STUDY_LOG_ZGTT_DAY, "KEY_t_subject_study_log_zgtt_day_PRIMARY", SubjectStudyLogZgttDay.SUBJECT_STUDY_LOG_ZGTT_DAY.ID);
        public static final UniqueKey<SubjectStudyLogZjDayRecord> KEY_T_SUBJECT_STUDY_LOG_ZJ_DAY_PRIMARY = createUniqueKey(SubjectStudyLogZjDay.SUBJECT_STUDY_LOG_ZJ_DAY, "KEY_t_subject_study_log_zj_day_PRIMARY", SubjectStudyLogZjDay.SUBJECT_STUDY_LOG_ZJ_DAY.ID);
        public static final UniqueKey<SubjectStudyLogZxDayRecord> KEY_T_SUBJECT_STUDY_LOG_ZX_DAY_PRIMARY = createUniqueKey(SubjectStudyLogZxDay.SUBJECT_STUDY_LOG_ZX_DAY, "KEY_t_subject_study_log_zx_day_PRIMARY", SubjectStudyLogZxDay.SUBJECT_STUDY_LOG_ZX_DAY.ID);
        public static final UniqueKey<SubjectTextAreaRecord> KEY_T_SUBJECT_TEXT_AREA_PRIMARY = createUniqueKey(SubjectTextArea.SUBJECT_TEXT_AREA, "KEY_t_subject_text_area_PRIMARY", SubjectTextArea.SUBJECT_TEXT_AREA.ID);
        public static final UniqueKey<SubjectYearBillRecord> KEY_T_SUBJECT_YEAR_BILL_PRIMARY = createUniqueKey(SubjectYearBill.SUBJECT_YEAR_BILL, "KEY_t_subject_year_bill_PRIMARY", SubjectYearBill.SUBJECT_YEAR_BILL.ID);
        public static final UniqueKey<SummaryRecord> KEY_T_SUMMARY_PRIMARY = createUniqueKey(Summary.SUMMARY, "KEY_t_summary_PRIMARY", Summary.SUMMARY.MEMBER_ID);
        public static final UniqueKey<SupplierRecord> KEY_T_SUPPLIER_PRIMARY = createUniqueKey(Supplier.SUPPLIER, "KEY_t_supplier_PRIMARY", Supplier.SUPPLIER.ID);
        public static final UniqueKey<TempMemberRecord> KEY_T_TEMP_MEMBER_PRIMARY = createUniqueKey(TempMember.TEMP_MEMBER, "KEY_t_temp_member_PRIMARY", TempMember.TEMP_MEMBER.ID);
        public static final UniqueKey<TempRepairCourseRecord> KEY_T_TEMP_REPAIR_COURSE_PRIMARY = createUniqueKey(TempRepairCourse.TEMP_REPAIR_COURSE, "KEY_t_temp_repair_course_PRIMARY", TempRepairCourse.TEMP_REPAIR_COURSE.ID);
        public static final UniqueKey<TempRepairSubjectRecord> KEY_T_TEMP_REPAIR_SUBJECT_PRIMARY = createUniqueKey(TempRepairSubject.TEMP_REPAIR_SUBJECT, "KEY_t_temp_repair_subject_PRIMARY", TempRepairSubject.TEMP_REPAIR_SUBJECT.ID);
        public static final UniqueKey<TempSectionStudyLogGt_24Record> KEY_T_TEMP_SECTION_STUDY_LOG_GT_24_PRIMARY = createUniqueKey(TempSectionStudyLogGt_24.TEMP_SECTION_STUDY_LOG_GT_24, "KEY_t_temp_section_study_log_gt_24_PRIMARY", TempSectionStudyLogGt_24.TEMP_SECTION_STUDY_LOG_GT_24.ID);
        public static final UniqueKey<TempSubjectSectionStudyLogRecord> KEY_T_TEMP_SUBJECT_SECTION_STUDY_LOG_PRIMARY = createUniqueKey(TempSubjectSectionStudyLog.TEMP_SUBJECT_SECTION_STUDY_LOG, "KEY_t_temp_subject_section_study_log_PRIMARY", TempSubjectSectionStudyLog.TEMP_SUBJECT_SECTION_STUDY_LOG.ID);
        public static final UniqueKey<ThematicRecord> KEY_T_THEMATIC_PRIMARY = createUniqueKey(Thematic.THEMATIC, "KEY_t_thematic_PRIMARY", Thematic.THEMATIC.ID);
        public static final UniqueKey<ThematicAttachmentRecord> KEY_T_THEMATIC_ATTACHMENT_PRIMARY = createUniqueKey(ThematicAttachment.THEMATIC_ATTACHMENT, "KEY_t_thematic_attachment_PRIMARY", ThematicAttachment.THEMATIC_ATTACHMENT.ID);
        public static final UniqueKey<ThematicChapterRecord> KEY_T_THEMATIC_CHAPTER_PRIMARY = createUniqueKey(ThematicChapter.THEMATIC_CHAPTER, "KEY_t_thematic_chapter_PRIMARY", ThematicChapter.THEMATIC_CHAPTER.ID);
        public static final UniqueKey<ThematicChapterSectionRecord> KEY_T_THEMATIC_CHAPTER_SECTION_PRIMARY = createUniqueKey(ThematicChapterSection.THEMATIC_CHAPTER_SECTION, "KEY_t_thematic_chapter_section_PRIMARY", ThematicChapterSection.THEMATIC_CHAPTER_SECTION.ID);
        public static final UniqueKey<ThematicMemberRecord> KEY_T_THEMATIC_MEMBER_PRIMARY = createUniqueKey(ThematicMember.THEMATIC_MEMBER, "KEY_t_thematic_member_PRIMARY", ThematicMember.THEMATIC_MEMBER.ID);
        public static final UniqueKey<ThematicNoticeRecord> KEY_T_THEMATIC_NOTICE_PRIMARY = createUniqueKey(ThematicNotice.THEMATIC_NOTICE, "KEY_t_thematic_notice_PRIMARY", ThematicNotice.THEMATIC_NOTICE.ID);
        public static final UniqueKey<ThematicWorkRecord> KEY_T_THEMATIC_WORK_PRIMARY = createUniqueKey(ThematicWork.THEMATIC_WORK, "KEY_t_thematic_work_PRIMARY", ThematicWork.THEMATIC_WORK.ID);
        public static final UniqueKey<ThirdPartyCallRecordRecord> KEY_T_THIRD_PARTY_CALL_RECORD_PRIMARY = createUniqueKey(ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD, "KEY_t_third_party_call_record_PRIMARY", ThirdPartyCallRecord.THIRD_PARTY_CALL_RECORD.ID);
        public static final UniqueKey<ThirdPartyCourseInfoRecord> KEY_T_THIRD_PARTY_COURSE_INFO_PRIMARY = createUniqueKey(ThirdPartyCourseInfo.THIRD_PARTY_COURSE_INFO, "KEY_t_third_party_course_info_PRIMARY", ThirdPartyCourseInfo.THIRD_PARTY_COURSE_INFO.ID);
        public static final UniqueKey<ThirdPartyCourseStudyProgressRecord> KEY_T_THIRD_PARTY_COURSE_STUDY_PROGRESS_PRIMARY = createUniqueKey(ThirdPartyCourseStudyProgress.THIRD_PARTY_COURSE_STUDY_PROGRESS, "KEY_t_third_party_course_study_progress_PRIMARY", ThirdPartyCourseStudyProgress.THIRD_PARTY_COURSE_STUDY_PROGRESS.ID);
        public static final UniqueKey<TopicRecord> KEY_T_TOPIC_PRIMARY = createUniqueKey(Topic.TOPIC, "KEY_t_topic_PRIMARY", Topic.TOPIC.ID);
        public static final UniqueKey<TopicRecord> KEY_T_TOPIC_UNIQUE_T_TOPIC_F_NAME_DELETE_FLAG = createUniqueKey(Topic.TOPIC, "KEY_t_topic_unique_t_topic_f_name_delete_flag", Topic.TOPIC.NAME, Topic.TOPIC.DELETE_FLAG);
        public static final UniqueKey<TopicObjectRecord> KEY_T_TOPIC_OBJECT_PRIMARY = createUniqueKey(TopicObject.TOPIC_OBJECT, "KEY_t_topic_object_PRIMARY", TopicObject.TOPIC_OBJECT.ID);
        public static final UniqueKey<WhiteRecordRecord> KEY_T_WHITE_RECORD_PRIMARY = createUniqueKey(WhiteRecord.WHITE_RECORD, "KEY_t_white_record_PRIMARY", WhiteRecord.WHITE_RECORD.ID);
        public static final UniqueKey<CourseSectionStudyProgressFfclc_2022Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022_PRIMARY = createUniqueKey(CourseSectionStudyProgressFfclc_2022.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022, "KEY_t_course_section_study_progress_ffclc_2022_PRIMARY", CourseSectionStudyProgressFfclc_2022.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022.ID);
        public static final UniqueKey<CourseSectionStudyProgressFfclc_2022Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022_UNIQUE_T_CSSP_FFCLC_2022_SECTION_MEMBER = createUniqueKey(CourseSectionStudyProgressFfclc_2022.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022, "KEY_t_course_section_study_progress_ffclc_2022_unique_t_cssp_ffclc_2022_section_member", CourseSectionStudyProgressFfclc_2022.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022.SECTION_ID, CourseSectionStudyProgressFfclc_2022.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022.MEMBER_ID);
        public static final UniqueKey<CourseInfoDjypRecord> KEY_T_COURSE_INFO_DJYP_PRIMARY = createUniqueKey(CourseInfoDjyp.COURSE_INFO_DJYP, "KEY_t_course_info_djyp_PRIMARY", CourseInfoDjyp.COURSE_INFO_DJYP.ID);
        public static final UniqueKey<CourseStudyPlanConfigRecord> KEY_T_COURSE_STUDY_PLAN_CONFIG_PRIMARY = createUniqueKey(CourseStudyPlanConfig.COURSE_STUDY_PLAN_CONFIG, "KEY_t_course_study_plan_config_PRIMARY", CourseStudyPlanConfig.COURSE_STUDY_PLAN_CONFIG.ID);
        public static final UniqueKey<MultidimensionalScoringRecord> KEY_T_MULTIDIMENSIONAL_SCORING_PRIMARY = createUniqueKey(MultidimensionalScoring.MULTIDIMENSIONAL_SCORING, "KEY_t_multidimensional_scoring_PRIMARY", MultidimensionalScoring.MULTIDIMENSIONAL_SCORING.ID);
        public static final UniqueKey<MultidimensionalScoringSubjectRecord> KEY_T_MULTIDIMENSIONAL_SCORING_SUBJECT_PRIMARY = createUniqueKey(MultidimensionalScoringSubject.MULTIDIMENSIONAL_SCORING_SUBJECT, "KEY_t_multidimensional_scoring_subject_PRIMARY", MultidimensionalScoringSubject.MULTIDIMENSIONAL_SCORING_SUBJECT.ID);
        public static final UniqueKey<MultidimensionalStudentScoreSheet_00Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_00_PRIMARY = createUniqueKey(MultidimensionalStudentScoreSheet_00.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_00, "KEY_t_multidimensional_student_score_sheet_00_PRIMARY", MultidimensionalStudentScoreSheet_00.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_00.ID);
        public static final UniqueKey<MultidimensionalStudentScoreSheet_01Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_01_PRIMARY = createUniqueKey(MultidimensionalStudentScoreSheet_01.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_01, "KEY_t_multidimensional_student_score_sheet_01_PRIMARY", MultidimensionalStudentScoreSheet_01.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_01.ID);
        public static final UniqueKey<MultidimensionalStudentScoreSheet_02Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_02_PRIMARY = createUniqueKey(MultidimensionalStudentScoreSheet_02.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_02, "KEY_t_multidimensional_student_score_sheet_02_PRIMARY", MultidimensionalStudentScoreSheet_02.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_02.ID);
        public static final UniqueKey<MultidimensionalStudentScoreSheet_03Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_03_PRIMARY = createUniqueKey(MultidimensionalStudentScoreSheet_03.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_03, "KEY_t_multidimensional_student_score_sheet_03_PRIMARY", MultidimensionalStudentScoreSheet_03.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_03.ID);
        public static final UniqueKey<MultidimensionalStudentScoreSheet_04Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_04_PRIMARY = createUniqueKey(MultidimensionalStudentScoreSheet_04.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_04, "KEY_t_multidimensional_student_score_sheet_04_PRIMARY", MultidimensionalStudentScoreSheet_04.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_04.ID);
        public static final UniqueKey<MultidimensionalStudentScoreSheet_05Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_05_PRIMARY = createUniqueKey(MultidimensionalStudentScoreSheet_05.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_05, "KEY_t_multidimensional_student_score_sheet_05_PRIMARY", MultidimensionalStudentScoreSheet_05.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_05.ID);
        public static final UniqueKey<MultidimensionalStudentScoreSheet_06Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_06_PRIMARY = createUniqueKey(MultidimensionalStudentScoreSheet_06.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_06, "KEY_t_multidimensional_student_score_sheet_06_PRIMARY", MultidimensionalStudentScoreSheet_06.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_06.ID);
        public static final UniqueKey<MultidimensionalStudentScoreSheet_07Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_07_PRIMARY = createUniqueKey(MultidimensionalStudentScoreSheet_07.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_07, "KEY_t_multidimensional_student_score_sheet_07_PRIMARY", MultidimensionalStudentScoreSheet_07.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_07.ID);
        public static final UniqueKey<MultidimensionalStudentScoreSheet_08Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_08_PRIMARY = createUniqueKey(MultidimensionalStudentScoreSheet_08.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_08, "KEY_t_multidimensional_student_score_sheet_08_PRIMARY", MultidimensionalStudentScoreSheet_08.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_08.ID);
        public static final UniqueKey<MultidimensionalStudentScoreSheet_09Record> KEY_T_MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09_PRIMARY = createUniqueKey(MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09, "KEY_t_multidimensional_student_score_sheet_09_PRIMARY", MultidimensionalStudentScoreSheet_09.MULTIDIMENSIONAL_STUDENT_SCORE_SHEET_09.ID);
        public static final UniqueKey<IntelligentBroadcastRecord> KEY_T_INTELLIGENT_BROADCAST_PRIMARY = createUniqueKey(IntelligentBroadcast.INTELLIGENT_BROADCAST, "KEY_t_intelligent_broadcast_PRIMARY", IntelligentBroadcast.INTELLIGENT_BROADCAST.ID);
        public static final UniqueKey<CaptionRecord> KEY_T_CAPTION_PRIMARY = createUniqueKey(Caption.CAPTION, "KEY_t_caption_PRIMARY", Caption.CAPTION.ID);


        public static final UniqueKey<CourseStudyProgressArchived_00Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_00_PRIMARY = createUniqueKey(CourseStudyProgressArchived_00.COURSE_STUDY_PROGRESS_ARCHIVED_00, "KEY_t_course_study_progress_archived_00_PRIMARY", CourseStudyProgressArchived_00.COURSE_STUDY_PROGRESS_ARCHIVED_00.ID);
        public static final UniqueKey<CourseStudyProgressArchived_00Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_00_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_00.COURSE_STUDY_PROGRESS_ARCHIVED_00, "KEY_t_course_study_progress_archived_00_unique_t_course_progress_member_course", CourseStudyProgressArchived_00.COURSE_STUDY_PROGRESS_ARCHIVED_00.MEMBER_ID, CourseStudyProgressArchived_00.COURSE_STUDY_PROGRESS_ARCHIVED_00.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_01Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_01_PRIMARY = createUniqueKey(CourseStudyProgressArchived_01.COURSE_STUDY_PROGRESS_ARCHIVED_01, "KEY_t_course_study_progress_archived_01_PRIMARY", CourseStudyProgressArchived_01.COURSE_STUDY_PROGRESS_ARCHIVED_01.ID);
        public static final UniqueKey<CourseStudyProgressArchived_01Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_01_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_01.COURSE_STUDY_PROGRESS_ARCHIVED_01, "KEY_t_course_study_progress_archived_01_unique_t_course_progress_member_course", CourseStudyProgressArchived_01.COURSE_STUDY_PROGRESS_ARCHIVED_01.MEMBER_ID, CourseStudyProgressArchived_01.COURSE_STUDY_PROGRESS_ARCHIVED_01.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_02Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_02_PRIMARY = createUniqueKey(CourseStudyProgressArchived_02.COURSE_STUDY_PROGRESS_ARCHIVED_02, "KEY_t_course_study_progress_archived_02_PRIMARY", CourseStudyProgressArchived_02.COURSE_STUDY_PROGRESS_ARCHIVED_02.ID);
        public static final UniqueKey<CourseStudyProgressArchived_02Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_02_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_02.COURSE_STUDY_PROGRESS_ARCHIVED_02, "KEY_t_course_study_progress_archived_02_unique_t_course_progress_member_course", CourseStudyProgressArchived_02.COURSE_STUDY_PROGRESS_ARCHIVED_02.MEMBER_ID, CourseStudyProgressArchived_02.COURSE_STUDY_PROGRESS_ARCHIVED_02.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_03Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_03_PRIMARY = createUniqueKey(CourseStudyProgressArchived_03.COURSE_STUDY_PROGRESS_ARCHIVED_03, "KEY_t_course_study_progress_archived_03_PRIMARY", CourseStudyProgressArchived_03.COURSE_STUDY_PROGRESS_ARCHIVED_03.ID);
        public static final UniqueKey<CourseStudyProgressArchived_03Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_03_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_03.COURSE_STUDY_PROGRESS_ARCHIVED_03, "KEY_t_course_study_progress_archived_03_unique_t_course_progress_member_course", CourseStudyProgressArchived_03.COURSE_STUDY_PROGRESS_ARCHIVED_03.MEMBER_ID, CourseStudyProgressArchived_03.COURSE_STUDY_PROGRESS_ARCHIVED_03.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_04Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_04_PRIMARY = createUniqueKey(CourseStudyProgressArchived_04.COURSE_STUDY_PROGRESS_ARCHIVED_04, "KEY_t_course_study_progress_archived_04_PRIMARY", CourseStudyProgressArchived_04.COURSE_STUDY_PROGRESS_ARCHIVED_04.ID);
        public static final UniqueKey<CourseStudyProgressArchived_04Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_04_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_04.COURSE_STUDY_PROGRESS_ARCHIVED_04, "KEY_t_course_study_progress_archived_04_unique_t_course_progress_member_course", CourseStudyProgressArchived_04.COURSE_STUDY_PROGRESS_ARCHIVED_04.MEMBER_ID, CourseStudyProgressArchived_04.COURSE_STUDY_PROGRESS_ARCHIVED_04.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_05Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_05_PRIMARY = createUniqueKey(CourseStudyProgressArchived_05.COURSE_STUDY_PROGRESS_ARCHIVED_05, "KEY_t_course_study_progress_archived_05_PRIMARY", CourseStudyProgressArchived_05.COURSE_STUDY_PROGRESS_ARCHIVED_05.ID);
        public static final UniqueKey<CourseStudyProgressArchived_05Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_05_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_05.COURSE_STUDY_PROGRESS_ARCHIVED_05, "KEY_t_course_study_progress_archived_05_unique_t_course_progress_member_course", CourseStudyProgressArchived_05.COURSE_STUDY_PROGRESS_ARCHIVED_05.MEMBER_ID, CourseStudyProgressArchived_05.COURSE_STUDY_PROGRESS_ARCHIVED_05.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_06Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_06_PRIMARY = createUniqueKey(CourseStudyProgressArchived_06.COURSE_STUDY_PROGRESS_ARCHIVED_06, "KEY_t_course_study_progress_archived_06_PRIMARY", CourseStudyProgressArchived_06.COURSE_STUDY_PROGRESS_ARCHIVED_06.ID);
        public static final UniqueKey<CourseStudyProgressArchived_06Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_06_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_06.COURSE_STUDY_PROGRESS_ARCHIVED_06, "KEY_t_course_study_progress_archived_06_unique_t_course_progress_member_course", CourseStudyProgressArchived_06.COURSE_STUDY_PROGRESS_ARCHIVED_06.MEMBER_ID, CourseStudyProgressArchived_06.COURSE_STUDY_PROGRESS_ARCHIVED_06.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_07Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_07_PRIMARY = createUniqueKey(CourseStudyProgressArchived_07.COURSE_STUDY_PROGRESS_ARCHIVED_07, "KEY_t_course_study_progress_archived_07_PRIMARY", CourseStudyProgressArchived_07.COURSE_STUDY_PROGRESS_ARCHIVED_07.ID);
        public static final UniqueKey<CourseStudyProgressArchived_07Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_07_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_07.COURSE_STUDY_PROGRESS_ARCHIVED_07, "KEY_t_course_study_progress_archived_07_unique_t_course_progress_member_course", CourseStudyProgressArchived_07.COURSE_STUDY_PROGRESS_ARCHIVED_07.MEMBER_ID, CourseStudyProgressArchived_07.COURSE_STUDY_PROGRESS_ARCHIVED_07.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_08Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_08_PRIMARY = createUniqueKey(CourseStudyProgressArchived_08.COURSE_STUDY_PROGRESS_ARCHIVED_08, "KEY_t_course_study_progress_archived_08_PRIMARY", CourseStudyProgressArchived_08.COURSE_STUDY_PROGRESS_ARCHIVED_08.ID);
        public static final UniqueKey<CourseStudyProgressArchived_08Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_08_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_08.COURSE_STUDY_PROGRESS_ARCHIVED_08, "KEY_t_course_study_progress_archived_08_unique_t_course_progress_member_course", CourseStudyProgressArchived_08.COURSE_STUDY_PROGRESS_ARCHIVED_08.MEMBER_ID, CourseStudyProgressArchived_08.COURSE_STUDY_PROGRESS_ARCHIVED_08.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_09Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_09_PRIMARY = createUniqueKey(CourseStudyProgressArchived_09.COURSE_STUDY_PROGRESS_ARCHIVED_09, "KEY_t_course_study_progress_archived_09_PRIMARY", CourseStudyProgressArchived_09.COURSE_STUDY_PROGRESS_ARCHIVED_09.ID);
        public static final UniqueKey<CourseStudyProgressArchived_09Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_09_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_09.COURSE_STUDY_PROGRESS_ARCHIVED_09, "KEY_t_course_study_progress_archived_09_unique_t_course_progress_member_course", CourseStudyProgressArchived_09.COURSE_STUDY_PROGRESS_ARCHIVED_09.MEMBER_ID, CourseStudyProgressArchived_09.COURSE_STUDY_PROGRESS_ARCHIVED_09.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_10Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_10_PRIMARY = createUniqueKey(CourseStudyProgressArchived_10.COURSE_STUDY_PROGRESS_ARCHIVED_10, "KEY_t_course_study_progress_archived_10_PRIMARY", CourseStudyProgressArchived_10.COURSE_STUDY_PROGRESS_ARCHIVED_10.ID);
        public static final UniqueKey<CourseStudyProgressArchived_10Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_10_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_10.COURSE_STUDY_PROGRESS_ARCHIVED_10, "KEY_t_course_study_progress_archived_10_unique_t_course_progress_member_course", CourseStudyProgressArchived_10.COURSE_STUDY_PROGRESS_ARCHIVED_10.MEMBER_ID, CourseStudyProgressArchived_10.COURSE_STUDY_PROGRESS_ARCHIVED_10.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_11Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_11_PRIMARY = createUniqueKey(CourseStudyProgressArchived_11.COURSE_STUDY_PROGRESS_ARCHIVED_11, "KEY_t_course_study_progress_archived_11_PRIMARY", CourseStudyProgressArchived_11.COURSE_STUDY_PROGRESS_ARCHIVED_11.ID);
        public static final UniqueKey<CourseStudyProgressArchived_11Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_11_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_11.COURSE_STUDY_PROGRESS_ARCHIVED_11, "KEY_t_course_study_progress_archived_11_unique_t_course_progress_member_course", CourseStudyProgressArchived_11.COURSE_STUDY_PROGRESS_ARCHIVED_11.MEMBER_ID, CourseStudyProgressArchived_11.COURSE_STUDY_PROGRESS_ARCHIVED_11.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_12Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_12_PRIMARY = createUniqueKey(CourseStudyProgressArchived_12.COURSE_STUDY_PROGRESS_ARCHIVED_12, "KEY_t_course_study_progress_archived_12_PRIMARY", CourseStudyProgressArchived_12.COURSE_STUDY_PROGRESS_ARCHIVED_12.ID);
        public static final UniqueKey<CourseStudyProgressArchived_12Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_12_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_12.COURSE_STUDY_PROGRESS_ARCHIVED_12, "KEY_t_course_study_progress_archived_12_unique_t_course_progress_member_course", CourseStudyProgressArchived_12.COURSE_STUDY_PROGRESS_ARCHIVED_12.MEMBER_ID, CourseStudyProgressArchived_12.COURSE_STUDY_PROGRESS_ARCHIVED_12.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_13Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_13_PRIMARY = createUniqueKey(CourseStudyProgressArchived_13.COURSE_STUDY_PROGRESS_ARCHIVED_13, "KEY_t_course_study_progress_archived_13_PRIMARY", CourseStudyProgressArchived_13.COURSE_STUDY_PROGRESS_ARCHIVED_13.ID);
        public static final UniqueKey<CourseStudyProgressArchived_13Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_13_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_13.COURSE_STUDY_PROGRESS_ARCHIVED_13, "KEY_t_course_study_progress_archived_13_unique_t_course_progress_member_course", CourseStudyProgressArchived_13.COURSE_STUDY_PROGRESS_ARCHIVED_13.MEMBER_ID, CourseStudyProgressArchived_13.COURSE_STUDY_PROGRESS_ARCHIVED_13.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_14Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_14_PRIMARY = createUniqueKey(CourseStudyProgressArchived_14.COURSE_STUDY_PROGRESS_ARCHIVED_14, "KEY_t_course_study_progress_archived_14_PRIMARY", CourseStudyProgressArchived_14.COURSE_STUDY_PROGRESS_ARCHIVED_14.ID);
        public static final UniqueKey<CourseStudyProgressArchived_14Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_14_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_14.COURSE_STUDY_PROGRESS_ARCHIVED_14, "KEY_t_course_study_progress_archived_14_unique_t_course_progress_member_course", CourseStudyProgressArchived_14.COURSE_STUDY_PROGRESS_ARCHIVED_14.MEMBER_ID, CourseStudyProgressArchived_14.COURSE_STUDY_PROGRESS_ARCHIVED_14.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_15Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_15_PRIMARY = createUniqueKey(CourseStudyProgressArchived_15.COURSE_STUDY_PROGRESS_ARCHIVED_15, "KEY_t_course_study_progress_archived_15_PRIMARY", CourseStudyProgressArchived_15.COURSE_STUDY_PROGRESS_ARCHIVED_15.ID);
        public static final UniqueKey<CourseStudyProgressArchived_15Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_15_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_15.COURSE_STUDY_PROGRESS_ARCHIVED_15, "KEY_t_course_study_progress_archived_15_unique_t_course_progress_member_course", CourseStudyProgressArchived_15.COURSE_STUDY_PROGRESS_ARCHIVED_15.MEMBER_ID, CourseStudyProgressArchived_15.COURSE_STUDY_PROGRESS_ARCHIVED_15.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_16Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_16_PRIMARY = createUniqueKey(CourseStudyProgressArchived_16.COURSE_STUDY_PROGRESS_ARCHIVED_16, "KEY_t_course_study_progress_archived_16_PRIMARY", CourseStudyProgressArchived_16.COURSE_STUDY_PROGRESS_ARCHIVED_16.ID);
        public static final UniqueKey<CourseStudyProgressArchived_16Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_16_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_16.COURSE_STUDY_PROGRESS_ARCHIVED_16, "KEY_t_course_study_progress_archived_16_unique_t_course_progress_member_course", CourseStudyProgressArchived_16.COURSE_STUDY_PROGRESS_ARCHIVED_16.MEMBER_ID, CourseStudyProgressArchived_16.COURSE_STUDY_PROGRESS_ARCHIVED_16.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_17Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_17_PRIMARY = createUniqueKey(CourseStudyProgressArchived_17.COURSE_STUDY_PROGRESS_ARCHIVED_17, "KEY_t_course_study_progress_archived_17_PRIMARY", CourseStudyProgressArchived_17.COURSE_STUDY_PROGRESS_ARCHIVED_17.ID);
        public static final UniqueKey<CourseStudyProgressArchived_17Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_17_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_17.COURSE_STUDY_PROGRESS_ARCHIVED_17, "KEY_t_course_study_progress_archived_17_unique_t_course_progress_member_course", CourseStudyProgressArchived_17.COURSE_STUDY_PROGRESS_ARCHIVED_17.MEMBER_ID, CourseStudyProgressArchived_17.COURSE_STUDY_PROGRESS_ARCHIVED_17.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_18Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_18_PRIMARY = createUniqueKey(CourseStudyProgressArchived_18.COURSE_STUDY_PROGRESS_ARCHIVED_18, "KEY_t_course_study_progress_archived_18_PRIMARY", CourseStudyProgressArchived_18.COURSE_STUDY_PROGRESS_ARCHIVED_18.ID);
        public static final UniqueKey<CourseStudyProgressArchived_18Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_18_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_18.COURSE_STUDY_PROGRESS_ARCHIVED_18, "KEY_t_course_study_progress_archived_18_unique_t_course_progress_member_course", CourseStudyProgressArchived_18.COURSE_STUDY_PROGRESS_ARCHIVED_18.MEMBER_ID, CourseStudyProgressArchived_18.COURSE_STUDY_PROGRESS_ARCHIVED_18.COURSE_ID);
        public static final UniqueKey<CourseStudyProgressArchived_19Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_19_PRIMARY = createUniqueKey(CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19, "KEY_t_course_study_progress_archived_19_PRIMARY", CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19.ID);
        public static final UniqueKey<CourseStudyProgressArchived_19Record> KEY_T_COURSE_STUDY_PROGRESS_ARCHIVED_19_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE = createUniqueKey(CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19, "KEY_t_course_study_progress_archived_19_unique_t_course_progress_member_course", CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19.MEMBER_ID, CourseStudyProgressArchived_19.COURSE_STUDY_PROGRESS_ARCHIVED_19.COURSE_ID);
        public static final UniqueKey<DeleteDataCourseRecord> KEY_T_DELETE_DATA_COURSE_PRIMARY = createUniqueKey(DeleteDataCourse.DELETE_DATA_COURSE, "KEY_t_delete_data_course_PRIMARY", DeleteDataCourse.DELETE_DATA_COURSE.ID);
        public static final UniqueKey<CourseRedShipAuditDetailRecord> KEY_T_COURSE_RED_SHIP_AUDIT_DETAIL_PRIMARY = createUniqueKey(CourseRedShipAuditDetail.COURSE_RED_SHIP_AUDIT_DETAIL, "KEY_t_course_red_ship_audit_detail_PRIMARY", CourseRedShipAuditDetail.COURSE_RED_SHIP_AUDIT_DETAIL.ID);
        public static final UniqueKey<CourseRedShipAuditVersionRecord> KEY_T_COURSE_RED_SHIP_AUDIT_VERSION_PRIMARY = createUniqueKey(CourseRedShipAuditVersion.COURSE_RED_SHIP_AUDIT_VERSION, "KEY_t_course_red_ship_audit_version_PRIMARY", CourseRedShipAuditVersion.COURSE_RED_SHIP_AUDIT_VERSION.ID);
        public static final UniqueKey<CourseRedShipAuditChapterSectionRecord> KEY_T_COURSE_RED_SHIP_AUDIT_CHAPTER_SECTION_PRIMARY = createUniqueKey(CourseRedShipAuditChapterSection.COURSE_RED_SHIP_AUDIT_CHAPTER_SECTION, "KEY_t_course_red_ship_audit_chapter_section_PRIMARY", CourseRedShipAuditChapterSection.COURSE_RED_SHIP_AUDIT_CHAPTER_SECTION.ID);
        public static final UniqueKey<IntelligentNoteRecord> KEY_T_INTELLIGENT_NOTE_PRIMARY = createUniqueKey(IntelligentNote.INTELLIGENT_NOTE, "KEY_t_intelligent_note_PRIMARY", IntelligentNote.INTELLIGENT_NOTE.ID);
        public static final UniqueKey<IntelligentNoteRecord> KEY_T_INTELLIGENT_NOTE_IDX_COURSE_SECTION_MEMBER = createUniqueKey(IntelligentNote.INTELLIGENT_NOTE, "KEY_t_intelligent_note_idx_course_section_member", IntelligentNote.INTELLIGENT_NOTE.COURSE_ID, IntelligentNote.INTELLIGENT_NOTE.SECTION_ID, IntelligentNote.INTELLIGENT_NOTE.CREATE_MEMBER_ID);

        public static final UniqueKey<IntelligentNoteBookmarkRecord> KEY_T_INTELLIGENT_NOTE_BOOKMARK_PRIMARY = createUniqueKey(IntelligentNoteBookmark.INTELLIGENT_NOTE_BOOKMARK, "KEY_t_intelligent_note_bookmark_PRIMARY", IntelligentNoteBookmark.INTELLIGENT_NOTE_BOOKMARK.ID);
        public static final UniqueKey<SubjectTopicManagerRecord> KEY_T_SUBJECT_TOPIC_MANAGER_PRIMARY = createUniqueKey(SubjectTopicManager.SUBJECT_TOPIC_MANAGER, "KEY_t_subject_topic_manager_PRIMARY", SubjectTopicManager.SUBJECT_TOPIC_MANAGER.ID);

        public static final UniqueKey<CourseSectionStudyProgressAhRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_AH_PRIMARY = createUniqueKey(CourseSectionStudyProgressAh.COURSE_SECTION_STUDY_PROGRESS_AH, "KEY_t_course_section_study_progress_ah_PRIMARY", CourseSectionStudyProgressAh.COURSE_SECTION_STUDY_PROGRESS_AH.ID);
        public static final UniqueKey<CourseSectionStudyProgressAhRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_AH_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressAh.COURSE_SECTION_STUDY_PROGRESS_AH, "KEY_t_course_section_study_progress_ah_unique_t_course_section_p_member_section", CourseSectionStudyProgressAh.COURSE_SECTION_STUDY_PROGRESS_AH.SECTION_ID, CourseSectionStudyProgressAh.COURSE_SECTION_STUDY_PROGRESS_AH.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressBjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_BJ_PRIMARY = createUniqueKey(CourseSectionStudyProgressBj.COURSE_SECTION_STUDY_PROGRESS_BJ, "KEY_t_course_section_study_progress_bj_PRIMARY", CourseSectionStudyProgressBj.COURSE_SECTION_STUDY_PROGRESS_BJ.ID);
        public static final UniqueKey<CourseSectionStudyProgressBjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_BJ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressBj.COURSE_SECTION_STUDY_PROGRESS_BJ, "KEY_t_course_section_study_progress_bj_unique_t_course_section_p_member_section", CourseSectionStudyProgressBj.COURSE_SECTION_STUDY_PROGRESS_BJ.SECTION_ID, CourseSectionStudyProgressBj.COURSE_SECTION_STUDY_PROGRESS_BJ.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressCmRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_CM_PRIMARY = createUniqueKey(CourseSectionStudyProgressCm.COURSE_SECTION_STUDY_PROGRESS_CM, "KEY_t_course_section_study_progress_cm_PRIMARY", CourseSectionStudyProgressCm.COURSE_SECTION_STUDY_PROGRESS_CM.ID);
        public static final UniqueKey<CourseSectionStudyProgressCmRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_CM_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressCm.COURSE_SECTION_STUDY_PROGRESS_CM, "KEY_t_course_section_study_progress_cm_unique_t_course_section_p_member_section", CourseSectionStudyProgressCm.COURSE_SECTION_STUDY_PROGRESS_CM.SECTION_ID, CourseSectionStudyProgressCm.COURSE_SECTION_STUDY_PROGRESS_CM.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressCqRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_CQ_PRIMARY = createUniqueKey(CourseSectionStudyProgressCq.COURSE_SECTION_STUDY_PROGRESS_CQ, "KEY_t_course_section_study_progress_cq_PRIMARY", CourseSectionStudyProgressCq.COURSE_SECTION_STUDY_PROGRESS_CQ.ID);
        public static final UniqueKey<CourseSectionStudyProgressCqRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_CQ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressCq.COURSE_SECTION_STUDY_PROGRESS_CQ, "KEY_t_course_section_study_progress_cq_unique_t_course_section_p_member_section", CourseSectionStudyProgressCq.COURSE_SECTION_STUDY_PROGRESS_CQ.SECTION_ID, CourseSectionStudyProgressCq.COURSE_SECTION_STUDY_PROGRESS_CQ.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressEbRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_EB_PRIMARY = createUniqueKey(CourseSectionStudyProgressEb.COURSE_SECTION_STUDY_PROGRESS_EB, "KEY_t_course_section_study_progress_eb_PRIMARY", CourseSectionStudyProgressEb.COURSE_SECTION_STUDY_PROGRESS_EB.ID);
        public static final UniqueKey<CourseSectionStudyProgressEbRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_EB_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressEb.COURSE_SECTION_STUDY_PROGRESS_EB, "KEY_t_course_section_study_progress_eb_unique_t_course_section_p_member_section", CourseSectionStudyProgressEb.COURSE_SECTION_STUDY_PROGRESS_EB.SECTION_ID, CourseSectionStudyProgressEb.COURSE_SECTION_STUDY_PROGRESS_EB.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressFjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FJ_PRIMARY = createUniqueKey(CourseSectionStudyProgressFj.COURSE_SECTION_STUDY_PROGRESS_FJ, "KEY_t_course_section_study_progress_fj_PRIMARY", CourseSectionStudyProgressFj.COURSE_SECTION_STUDY_PROGRESS_FJ.ID);
        public static final UniqueKey<CourseSectionStudyProgressFjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FJ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressFj.COURSE_SECTION_STUDY_PROGRESS_FJ, "KEY_t_course_section_study_progress_fj_unique_t_course_section_p_member_section", CourseSectionStudyProgressFj.COURSE_SECTION_STUDY_PROGRESS_FJ.SECTION_ID, CourseSectionStudyProgressFj.COURSE_SECTION_STUDY_PROGRESS_FJ.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressGdRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_GD_PRIMARY = createUniqueKey(CourseSectionStudyProgressGd.COURSE_SECTION_STUDY_PROGRESS_GD, "KEY_t_course_section_study_progress_gd_PRIMARY", CourseSectionStudyProgressGd.COURSE_SECTION_STUDY_PROGRESS_GD.ID);
        public static final UniqueKey<CourseSectionStudyProgressGdRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_GD_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressGd.COURSE_SECTION_STUDY_PROGRESS_GD, "KEY_t_course_section_study_progress_gd_unique_t_course_section_p_member_section", CourseSectionStudyProgressGd.COURSE_SECTION_STUDY_PROGRESS_GD.SECTION_ID, CourseSectionStudyProgressGd.COURSE_SECTION_STUDY_PROGRESS_GD.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressGsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_GS_PRIMARY = createUniqueKey(CourseSectionStudyProgressGs.COURSE_SECTION_STUDY_PROGRESS_GS, "KEY_t_course_section_study_progress_gs_PRIMARY", CourseSectionStudyProgressGs.COURSE_SECTION_STUDY_PROGRESS_GS.ID);
        public static final UniqueKey<CourseSectionStudyProgressGsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_GS_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressGs.COURSE_SECTION_STUDY_PROGRESS_GS, "KEY_t_course_section_study_progress_gs_unique_t_course_section_p_member_section", CourseSectionStudyProgressGs.COURSE_SECTION_STUDY_PROGRESS_GS.SECTION_ID, CourseSectionStudyProgressGs.COURSE_SECTION_STUDY_PROGRESS_GS.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressGxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_GX_PRIMARY = createUniqueKey(CourseSectionStudyProgressGx.COURSE_SECTION_STUDY_PROGRESS_GX, "KEY_t_course_section_study_progress_gx_PRIMARY", CourseSectionStudyProgressGx.COURSE_SECTION_STUDY_PROGRESS_GX.ID);
        public static final UniqueKey<CourseSectionStudyProgressGxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_GX_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressGx.COURSE_SECTION_STUDY_PROGRESS_GX, "KEY_t_course_section_study_progress_gx_unique_t_course_section_p_member_section", CourseSectionStudyProgressGx.COURSE_SECTION_STUDY_PROGRESS_GX.SECTION_ID, CourseSectionStudyProgressGx.COURSE_SECTION_STUDY_PROGRESS_GX.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressGzRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_GZ_PRIMARY = createUniqueKey(CourseSectionStudyProgressGz.COURSE_SECTION_STUDY_PROGRESS_GZ, "KEY_t_course_section_study_progress_gz_PRIMARY", CourseSectionStudyProgressGz.COURSE_SECTION_STUDY_PROGRESS_GZ.ID);
        public static final UniqueKey<CourseSectionStudyProgressGzRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_GZ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressGz.COURSE_SECTION_STUDY_PROGRESS_GZ, "KEY_t_course_section_study_progress_gz_unique_t_course_section_p_member_section", CourseSectionStudyProgressGz.COURSE_SECTION_STUDY_PROGRESS_GZ.SECTION_ID, CourseSectionStudyProgressGz.COURSE_SECTION_STUDY_PROGRESS_GZ.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressHbRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_HB_PRIMARY = createUniqueKey(CourseSectionStudyProgressHb.COURSE_SECTION_STUDY_PROGRESS_HB, "KEY_t_course_section_study_progress_hb_PRIMARY", CourseSectionStudyProgressHb.COURSE_SECTION_STUDY_PROGRESS_HB.ID);
        public static final UniqueKey<CourseSectionStudyProgressHbRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_HB_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressHb.COURSE_SECTION_STUDY_PROGRESS_HB, "KEY_t_course_section_study_progress_hb_unique_t_course_section_p_member_section", CourseSectionStudyProgressHb.COURSE_SECTION_STUDY_PROGRESS_HB.SECTION_ID, CourseSectionStudyProgressHb.COURSE_SECTION_STUDY_PROGRESS_HB.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressHlRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_HL_PRIMARY = createUniqueKey(CourseSectionStudyProgressHl.COURSE_SECTION_STUDY_PROGRESS_HL, "KEY_t_course_section_study_progress_hl_PRIMARY", CourseSectionStudyProgressHl.COURSE_SECTION_STUDY_PROGRESS_HL.ID);
        public static final UniqueKey<CourseSectionStudyProgressHlRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_HL_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressHl.COURSE_SECTION_STUDY_PROGRESS_HL, "KEY_t_course_section_study_progress_hl_unique_t_course_section_p_member_section", CourseSectionStudyProgressHl.COURSE_SECTION_STUDY_PROGRESS_HL.SECTION_ID, CourseSectionStudyProgressHl.COURSE_SECTION_STUDY_PROGRESS_HL.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressHnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_HN_PRIMARY = createUniqueKey(CourseSectionStudyProgressHn.COURSE_SECTION_STUDY_PROGRESS_HN, "KEY_t_course_section_study_progress_hn_PRIMARY", CourseSectionStudyProgressHn.COURSE_SECTION_STUDY_PROGRESS_HN.ID);
        public static final UniqueKey<CourseSectionStudyProgressHnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_HN_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressHn.COURSE_SECTION_STUDY_PROGRESS_HN, "KEY_t_course_section_study_progress_hn_unique_t_course_section_p_member_section", CourseSectionStudyProgressHn.COURSE_SECTION_STUDY_PROGRESS_HN.SECTION_ID, CourseSectionStudyProgressHn.COURSE_SECTION_STUDY_PROGRESS_HN.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressJlRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_JL_PRIMARY = createUniqueKey(CourseSectionStudyProgressJl.COURSE_SECTION_STUDY_PROGRESS_JL, "KEY_t_course_section_study_progress_jl_PRIMARY", CourseSectionStudyProgressJl.COURSE_SECTION_STUDY_PROGRESS_JL.ID);
        public static final UniqueKey<CourseSectionStudyProgressJlRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_JL_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressJl.COURSE_SECTION_STUDY_PROGRESS_JL, "KEY_t_course_section_study_progress_jl_unique_t_course_section_p_member_section", CourseSectionStudyProgressJl.COURSE_SECTION_STUDY_PROGRESS_JL.SECTION_ID, CourseSectionStudyProgressJl.COURSE_SECTION_STUDY_PROGRESS_JL.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressJsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_JS_PRIMARY = createUniqueKey(CourseSectionStudyProgressJs.COURSE_SECTION_STUDY_PROGRESS_JS, "KEY_t_course_section_study_progress_js_PRIMARY", CourseSectionStudyProgressJs.COURSE_SECTION_STUDY_PROGRESS_JS.ID);
        public static final UniqueKey<CourseSectionStudyProgressJsRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_JS_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressJs.COURSE_SECTION_STUDY_PROGRESS_JS, "KEY_t_course_section_study_progress_js_unique_t_course_section_p_member_section", CourseSectionStudyProgressJs.COURSE_SECTION_STUDY_PROGRESS_JS.SECTION_ID, CourseSectionStudyProgressJs.COURSE_SECTION_STUDY_PROGRESS_JS.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressJxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_JX_PRIMARY = createUniqueKey(CourseSectionStudyProgressJx.COURSE_SECTION_STUDY_PROGRESS_JX, "KEY_t_course_section_study_progress_jx_PRIMARY", CourseSectionStudyProgressJx.COURSE_SECTION_STUDY_PROGRESS_JX.ID);
        public static final UniqueKey<CourseSectionStudyProgressJxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_JX_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressJx.COURSE_SECTION_STUDY_PROGRESS_JX, "KEY_t_course_section_study_progress_jx_unique_t_course_section_p_member_section", CourseSectionStudyProgressJx.COURSE_SECTION_STUDY_PROGRESS_JX.SECTION_ID, CourseSectionStudyProgressJx.COURSE_SECTION_STUDY_PROGRESS_JX.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressLnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_LN_PRIMARY = createUniqueKey(CourseSectionStudyProgressLn.COURSE_SECTION_STUDY_PROGRESS_LN, "KEY_t_course_section_study_progress_ln_PRIMARY", CourseSectionStudyProgressLn.COURSE_SECTION_STUDY_PROGRESS_LN.ID);
        public static final UniqueKey<CourseSectionStudyProgressLnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_LN_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressLn.COURSE_SECTION_STUDY_PROGRESS_LN, "KEY_t_course_section_study_progress_ln_unique_t_course_section_p_member_section", CourseSectionStudyProgressLn.COURSE_SECTION_STUDY_PROGRESS_LN.SECTION_ID, CourseSectionStudyProgressLn.COURSE_SECTION_STUDY_PROGRESS_LN.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressNmRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_NM_PRIMARY = createUniqueKey(CourseSectionStudyProgressNm.COURSE_SECTION_STUDY_PROGRESS_NM, "KEY_t_course_section_study_progress_nm_PRIMARY", CourseSectionStudyProgressNm.COURSE_SECTION_STUDY_PROGRESS_NM.ID);
        public static final UniqueKey<CourseSectionStudyProgressNmRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_NM_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressNm.COURSE_SECTION_STUDY_PROGRESS_NM, "KEY_t_course_section_study_progress_nm_unique_t_course_section_p_member_section", CourseSectionStudyProgressNm.COURSE_SECTION_STUDY_PROGRESS_NM.SECTION_ID, CourseSectionStudyProgressNm.COURSE_SECTION_STUDY_PROGRESS_NM.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressNxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_NX_PRIMARY = createUniqueKey(CourseSectionStudyProgressNx.COURSE_SECTION_STUDY_PROGRESS_NX, "KEY_t_course_section_study_progress_nx_PRIMARY", CourseSectionStudyProgressNx.COURSE_SECTION_STUDY_PROGRESS_NX.ID);
        public static final UniqueKey<CourseSectionStudyProgressNxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_NX_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressNx.COURSE_SECTION_STUDY_PROGRESS_NX, "KEY_t_course_section_study_progress_nx_unique_t_course_section_p_member_section", CourseSectionStudyProgressNx.COURSE_SECTION_STUDY_PROGRESS_NX.SECTION_ID, CourseSectionStudyProgressNx.COURSE_SECTION_STUDY_PROGRESS_NX.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressOtherRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_OTHER_PRIMARY = createUniqueKey(CourseSectionStudyProgressOther.COURSE_SECTION_STUDY_PROGRESS_OTHER, "KEY_t_course_section_study_progress_other_PRIMARY", CourseSectionStudyProgressOther.COURSE_SECTION_STUDY_PROGRESS_OTHER.ID);
        public static final UniqueKey<CourseSectionStudyProgressOtherRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_OTHER_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressOther.COURSE_SECTION_STUDY_PROGRESS_OTHER, "KEY_t_course_section_study_progress_other_unique_t_course_section_p_member_section", CourseSectionStudyProgressOther.COURSE_SECTION_STUDY_PROGRESS_OTHER.SECTION_ID, CourseSectionStudyProgressOther.COURSE_SECTION_STUDY_PROGRESS_OTHER.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressQhRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_QH_PRIMARY = createUniqueKey(CourseSectionStudyProgressQh.COURSE_SECTION_STUDY_PROGRESS_QH, "KEY_t_course_section_study_progress_qh_PRIMARY", CourseSectionStudyProgressQh.COURSE_SECTION_STUDY_PROGRESS_QH.ID);
        public static final UniqueKey<CourseSectionStudyProgressQhRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_QH_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressQh.COURSE_SECTION_STUDY_PROGRESS_QH, "KEY_t_course_section_study_progress_qh_unique_t_course_section_p_member_section", CourseSectionStudyProgressQh.COURSE_SECTION_STUDY_PROGRESS_QH.SECTION_ID, CourseSectionStudyProgressQh.COURSE_SECTION_STUDY_PROGRESS_QH.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressQoRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_QO_PRIMARY = createUniqueKey(CourseSectionStudyProgressQo.COURSE_SECTION_STUDY_PROGRESS_QO, "KEY_t_course_section_study_progress_qo_PRIMARY", CourseSectionStudyProgressQo.COURSE_SECTION_STUDY_PROGRESS_QO.ID);
        public static final UniqueKey<CourseSectionStudyProgressQoRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_QO_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressQo.COURSE_SECTION_STUDY_PROGRESS_QO, "KEY_t_course_section_study_progress_qo_unique_t_course_section_p_member_section", CourseSectionStudyProgressQo.COURSE_SECTION_STUDY_PROGRESS_QO.SECTION_ID, CourseSectionStudyProgressQo.COURSE_SECTION_STUDY_PROGRESS_QO.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressScRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SC_PRIMARY = createUniqueKey(CourseSectionStudyProgressSc.COURSE_SECTION_STUDY_PROGRESS_SC, "KEY_t_course_section_study_progress_sc_PRIMARY", CourseSectionStudyProgressSc.COURSE_SECTION_STUDY_PROGRESS_SC.ID);
        public static final UniqueKey<CourseSectionStudyProgressScRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SC_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressSc.COURSE_SECTION_STUDY_PROGRESS_SC, "KEY_t_course_section_study_progress_sc_unique_t_course_section_p_member_section", CourseSectionStudyProgressSc.COURSE_SECTION_STUDY_PROGRESS_SC.SECTION_ID, CourseSectionStudyProgressSc.COURSE_SECTION_STUDY_PROGRESS_SC.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressSdRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SD_PRIMARY = createUniqueKey(CourseSectionStudyProgressSd.COURSE_SECTION_STUDY_PROGRESS_SD, "KEY_t_course_section_study_progress_sd_PRIMARY", CourseSectionStudyProgressSd.COURSE_SECTION_STUDY_PROGRESS_SD.ID);
        public static final UniqueKey<CourseSectionStudyProgressSdRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SD_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressSd.COURSE_SECTION_STUDY_PROGRESS_SD, "KEY_t_course_section_study_progress_sd_unique_t_course_section_p_member_section", CourseSectionStudyProgressSd.COURSE_SECTION_STUDY_PROGRESS_SD.SECTION_ID, CourseSectionStudyProgressSd.COURSE_SECTION_STUDY_PROGRESS_SD.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressShRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SH_PRIMARY = createUniqueKey(CourseSectionStudyProgressSh.COURSE_SECTION_STUDY_PROGRESS_SH, "KEY_t_course_section_study_progress_sh_PRIMARY", CourseSectionStudyProgressSh.COURSE_SECTION_STUDY_PROGRESS_SH.ID);
        public static final UniqueKey<CourseSectionStudyProgressShRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SH_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressSh.COURSE_SECTION_STUDY_PROGRESS_SH, "KEY_t_course_section_study_progress_sh_unique_t_course_section_p_member_section", CourseSectionStudyProgressSh.COURSE_SECTION_STUDY_PROGRESS_SH.SECTION_ID, CourseSectionStudyProgressSh.COURSE_SECTION_STUDY_PROGRESS_SH.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressSnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SN_PRIMARY = createUniqueKey(CourseSectionStudyProgressSn.COURSE_SECTION_STUDY_PROGRESS_SN, "KEY_t_course_section_study_progress_sn_PRIMARY", CourseSectionStudyProgressSn.COURSE_SECTION_STUDY_PROGRESS_SN.ID);
        public static final UniqueKey<CourseSectionStudyProgressSnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SN_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressSn.COURSE_SECTION_STUDY_PROGRESS_SN, "KEY_t_course_section_study_progress_sn_unique_t_course_section_p_member_section", CourseSectionStudyProgressSn.COURSE_SECTION_STUDY_PROGRESS_SN.SECTION_ID, CourseSectionStudyProgressSn.COURSE_SECTION_STUDY_PROGRESS_SN.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressSxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SX_PRIMARY = createUniqueKey(CourseSectionStudyProgressSx.COURSE_SECTION_STUDY_PROGRESS_SX, "KEY_t_course_section_study_progress_sx_PRIMARY", CourseSectionStudyProgressSx.COURSE_SECTION_STUDY_PROGRESS_SX.ID);
        public static final UniqueKey<CourseSectionStudyProgressSxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_SX_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressSx.COURSE_SECTION_STUDY_PROGRESS_SX, "KEY_t_course_section_study_progress_sx_unique_t_course_section_p_member_section", CourseSectionStudyProgressSx.COURSE_SECTION_STUDY_PROGRESS_SX.SECTION_ID, CourseSectionStudyProgressSx.COURSE_SECTION_STUDY_PROGRESS_SX.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressTjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_TJ_PRIMARY = createUniqueKey(CourseSectionStudyProgressTj.COURSE_SECTION_STUDY_PROGRESS_TJ, "KEY_t_course_section_study_progress_tj_PRIMARY", CourseSectionStudyProgressTj.COURSE_SECTION_STUDY_PROGRESS_TJ.ID);
        public static final UniqueKey<CourseSectionStudyProgressTjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_TJ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressTj.COURSE_SECTION_STUDY_PROGRESS_TJ, "KEY_t_course_section_study_progress_tj_unique_t_course_section_p_member_section", CourseSectionStudyProgressTj.COURSE_SECTION_STUDY_PROGRESS_TJ.SECTION_ID, CourseSectionStudyProgressTj.COURSE_SECTION_STUDY_PROGRESS_TJ.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressXjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XJ_PRIMARY = createUniqueKey(CourseSectionStudyProgressXj.COURSE_SECTION_STUDY_PROGRESS_XJ, "KEY_t_course_section_study_progress_xj_PRIMARY", CourseSectionStudyProgressXj.COURSE_SECTION_STUDY_PROGRESS_XJ.ID);
        public static final UniqueKey<CourseSectionStudyProgressXjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XJ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressXj.COURSE_SECTION_STUDY_PROGRESS_XJ, "KEY_t_course_section_study_progress_xj_unique_t_course_section_p_member_section", CourseSectionStudyProgressXj.COURSE_SECTION_STUDY_PROGRESS_XJ.SECTION_ID, CourseSectionStudyProgressXj.COURSE_SECTION_STUDY_PROGRESS_XJ.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressXnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XN_PRIMARY = createUniqueKey(CourseSectionStudyProgressXn.COURSE_SECTION_STUDY_PROGRESS_XN, "KEY_t_course_section_study_progress_xn_PRIMARY", CourseSectionStudyProgressXn.COURSE_SECTION_STUDY_PROGRESS_XN.ID);
        public static final UniqueKey<CourseSectionStudyProgressXnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XN_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressXn.COURSE_SECTION_STUDY_PROGRESS_XN, "KEY_t_course_section_study_progress_xn_unique_t_course_section_p_member_section", CourseSectionStudyProgressXn.COURSE_SECTION_STUDY_PROGRESS_XN.SECTION_ID, CourseSectionStudyProgressXn.COURSE_SECTION_STUDY_PROGRESS_XN.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressXzRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XZ_PRIMARY = createUniqueKey(CourseSectionStudyProgressXz.COURSE_SECTION_STUDY_PROGRESS_XZ, "KEY_t_course_section_study_progress_xz_PRIMARY", CourseSectionStudyProgressXz.COURSE_SECTION_STUDY_PROGRESS_XZ.ID);
        public static final UniqueKey<CourseSectionStudyProgressXzRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_XZ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressXz.COURSE_SECTION_STUDY_PROGRESS_XZ, "KEY_t_course_section_study_progress_xz_unique_t_course_section_p_member_section", CourseSectionStudyProgressXz.COURSE_SECTION_STUDY_PROGRESS_XZ.SECTION_ID, CourseSectionStudyProgressXz.COURSE_SECTION_STUDY_PROGRESS_XZ.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressYnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_YN_PRIMARY = createUniqueKey(CourseSectionStudyProgressYn.COURSE_SECTION_STUDY_PROGRESS_YN, "KEY_t_course_section_study_progress_yn_PRIMARY", CourseSectionStudyProgressYn.COURSE_SECTION_STUDY_PROGRESS_YN.ID);
        public static final UniqueKey<CourseSectionStudyProgressYnRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_YN_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressYn.COURSE_SECTION_STUDY_PROGRESS_YN, "KEY_t_course_section_study_progress_yn_unique_t_course_section_p_member_section", CourseSectionStudyProgressYn.COURSE_SECTION_STUDY_PROGRESS_YN.SECTION_ID, CourseSectionStudyProgressYn.COURSE_SECTION_STUDY_PROGRESS_YN.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressZgttRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZGTT_PRIMARY = createUniqueKey(CourseSectionStudyProgressZgtt.COURSE_SECTION_STUDY_PROGRESS_ZGTT, "KEY_t_course_section_study_progress_zgtt_PRIMARY", CourseSectionStudyProgressZgtt.COURSE_SECTION_STUDY_PROGRESS_ZGTT.ID);
        public static final UniqueKey<CourseSectionStudyProgressZgttRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZGTT_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressZgtt.COURSE_SECTION_STUDY_PROGRESS_ZGTT, "KEY_t_course_section_study_progress_zgtt_unique_t_course_section_p_member_section", CourseSectionStudyProgressZgtt.COURSE_SECTION_STUDY_PROGRESS_ZGTT.SECTION_ID, CourseSectionStudyProgressZgtt.COURSE_SECTION_STUDY_PROGRESS_ZGTT.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressZjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZJ_PRIMARY = createUniqueKey(CourseSectionStudyProgressZj.COURSE_SECTION_STUDY_PROGRESS_ZJ, "KEY_t_course_section_study_progress_zj_PRIMARY", CourseSectionStudyProgressZj.COURSE_SECTION_STUDY_PROGRESS_ZJ.ID);
        public static final UniqueKey<CourseSectionStudyProgressZjRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZJ_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressZj.COURSE_SECTION_STUDY_PROGRESS_ZJ, "KEY_t_course_section_study_progress_zj_unique_t_course_section_p_member_section", CourseSectionStudyProgressZj.COURSE_SECTION_STUDY_PROGRESS_ZJ.SECTION_ID, CourseSectionStudyProgressZj.COURSE_SECTION_STUDY_PROGRESS_ZJ.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressZxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZX_PRIMARY = createUniqueKey(CourseSectionStudyProgressZx.COURSE_SECTION_STUDY_PROGRESS_ZX, "KEY_t_course_section_study_progress_zx_PRIMARY", CourseSectionStudyProgressZx.COURSE_SECTION_STUDY_PROGRESS_ZX.ID);
        public static final UniqueKey<CourseSectionStudyProgressZxRecord> KEY_T_COURSE_SECTION_STUDY_PROGRESS_ZX_UNIQUE_T_COURSE_SECTION_P_MEMBER_SECTION = createUniqueKey(CourseSectionStudyProgressZx.COURSE_SECTION_STUDY_PROGRESS_ZX, "KEY_t_course_section_study_progress_zx_unique_t_course_section_p_member_section", CourseSectionStudyProgressZx.COURSE_SECTION_STUDY_PROGRESS_ZX.SECTION_ID, CourseSectionStudyProgressZx.COURSE_SECTION_STUDY_PROGRESS_ZX.MEMBER_ID);
        public static final UniqueKey<AnnualBill_2022Record> KEY_T_ANNUAL_BILL_2022_PRIMARY = createUniqueKey(AnnualBill_2022.ANNUAL_BILL_2022, "KEY_t_annual_bill_2022_PRIMARY", AnnualBill_2022.ANNUAL_BILL_2022.ID);
        public static final UniqueKey<BusinessEmergencyRecord> KEY_T_BUSINESS_EMERGENCY_PRIMARY = createUniqueKey(BusinessEmergency.BUSINESS_EMERGENCY, "KEY_t_business_emergency_PRIMARY", BusinessEmergency.BUSINESS_EMERGENCY.ID);
        public static final UniqueKey<CourseSectionStudyProgressFfclc_2023Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023_PRIMARY = createUniqueKey(CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023, "KEY_t_course_section_study_progress_ffclc_2023_PRIMARY", CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.ID);
        public static final UniqueKey<CourseSectionStudyProgressFfclc_2023Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023_UNIQUE_T_CSSP_FFCLC_2023_SECTION_MEMBER = createUniqueKey(CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023, "KEY_t_course_section_study_progress_ffclc_2023_unique_t_cssp_ffclc_2023_section_member", CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.SECTION_ID, CourseSectionStudyProgressFfclc_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressFfcls_2023Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_2023_PRIMARY = createUniqueKey(CourseSectionStudyProgressFfcls_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2023, "KEY_t_course_section_study_progress_ffcls_2023_PRIMARY", CourseSectionStudyProgressFfcls_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2023.ID);
        public static final UniqueKey<CourseSectionStudyProgressFfcls_2023Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_2023_UNIQUE_T_CSSP_FFCLS_2023_SECTION_MEMBER = createUniqueKey(CourseSectionStudyProgressFfcls_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2023, "KEY_t_course_section_study_progress_ffcls_2023_unique_t_cssp_ffcls_2023_section_member", CourseSectionStudyProgressFfcls_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2023.SECTION_ID, CourseSectionStudyProgressFfcls_2023.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2023.MEMBER_ID);
        public static final UniqueKey<AuthenticatedGroupRecord> KEY_T_AUTHENTICATED_GROUP_PRIMARY = createUniqueKey(AuthenticatedGroup.AUTHENTICATED_GROUP, "KEY_t_authenticated_group_PRIMARY", AuthenticatedGroup.AUTHENTICATED_GROUP.ID);
        public static final UniqueKey<AuthenticatedZoneRecord> KEY_T_AUTHENTICATED_ZONE_PRIMARY = createUniqueKey(AuthenticatedZone.AUTHENTICATED_ZONE, "KEY_t_authenticated_zone_PRIMARY", AuthenticatedZone.AUTHENTICATED_ZONE.ID);
        public static final UniqueKey<AuthenticatedZoneGroupRecord> KEY_T_AUTHENTICATED_ZONE_GROUP_PRIMARY = createUniqueKey(AuthenticatedZoneGroup.AUTHENTICATED_ZONE_GROUP, "KEY_t_authenticated_zone_group_PRIMARY", AuthenticatedZoneGroup.AUTHENTICATED_ZONE_GROUP.ID);
        public static final UniqueKey<SubAuthenticatedRecord> KEY_T_SUB_AUTHENTICATED_PRIMARY = createUniqueKey(SubAuthenticated.SUB_AUTHENTICATED, "KEY_t_sub_authenticated_PRIMARY", SubAuthenticated.SUB_AUTHENTICATED.ID);
        public static final UniqueKey<SubAuthenticatedCertificateRecordRecord> KEY_T_SUB_AUTHENTICATED_CERTIFICATE_RECORD_PRIMARY = createUniqueKey(SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD, "KEY_t_sub_authenticated_certificate_record_PRIMARY", SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.ID);
        public static final UniqueKey<SubAuthenticatedContentConfigureRecord> KEY_T_SUB_AUTHENTICATED_CONTENT_CONFIGURE_PRIMARY = createUniqueKey(SubAuthenticatedContentConfigure.SUB_AUTHENTICATED_CONTENT_CONFIGURE, "KEY_t_sub_authenticated_content_configure_PRIMARY", SubAuthenticatedContentConfigure.SUB_AUTHENTICATED_CONTENT_CONFIGURE.ID);
        public static final UniqueKey<SubAuthenticatedContentConfigureRecord> KEY_T_SUB_AUTHENTICATED_CONTENT_CONFIGURE_T_SUB_AUTHENTICATED_CONTENT_CONFIG_CONTENT_IDX = createUniqueKey(SubAuthenticatedContentConfigure.SUB_AUTHENTICATED_CONTENT_CONFIGURE, "KEY_t_sub_authenticated_content_configure_t_sub_authenticated_content_config_content_IDX", SubAuthenticatedContentConfigure.SUB_AUTHENTICATED_CONTENT_CONFIGURE.CONTENT_ID);        public static final UniqueKey<SubAuthenticatedCourseProgressRecord> KEY_T_SUB_AUTHENTICATED_COURSE_PROGRESS_PRIMARY = createUniqueKey(SubAuthenticatedCourseProgress.SUB_AUTHENTICATED_COURSE_PROGRESS, "KEY_t_sub_authenticated_course_progress_PRIMARY", SubAuthenticatedCourseProgress.SUB_AUTHENTICATED_COURSE_PROGRESS.ID);
        public static final UniqueKey<SubAuthenticatedResourceAuditRecordRecord> KEY_T_SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD_PRIMARY = createUniqueKey(SubAuthenticatedResourceAuditRecord.SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD, "KEY_t_sub_authenticated_resource_audit_record_PRIMARY", SubAuthenticatedResourceAuditRecord.SUB_AUTHENTICATED_RESOURCE_AUDIT_RECORD.ID);
        public static final UniqueKey<SubAuthenticatedStudyOnlineRecord> KEY_T_SUB_AUTHENTICATED_STUDY_ONLINE_PRIMARY = createUniqueKey(SubAuthenticatedStudyOnline.SUB_AUTHENTICATED_STUDY_ONLINE, "KEY_t_sub_authenticated_study_online_PRIMARY", SubAuthenticatedStudyOnline.SUB_AUTHENTICATED_STUDY_ONLINE.ID);
        public static final UniqueKey<SubAuthenticatedDimensionRecord> KEY_T_SUB_AUTHENTICATED_DIMENSION_PRIMARY = createUniqueKey(SubAuthenticatedDimension.SUB_AUTHENTICATED_DIMENSION, "KEY_t_sub_authenticated_dimension_PRIMARY", SubAuthenticatedDimension.SUB_AUTHENTICATED_DIMENSION.ID);
        public static final UniqueKey<SubAuthenticatedMemberDimensionRecord> KEY_T_SUB_AUTHENTICATED_MEMBER_DIMENSION_PRIMARY = createUniqueKey(SubAuthenticatedMemberDimension.SUB_AUTHENTICATED_MEMBER_DIMENSION, "KEY_t_sub_authenticated_member_dimension_PRIMARY", SubAuthenticatedMemberDimension.SUB_AUTHENTICATED_MEMBER_DIMENSION.ID);
        public static final UniqueKey<SubAuthenticatedRegisterRecord> KEY_T_SUB_AUTHENTICATED_REGISTER_PRIMARY = createUniqueKey(SubAuthenticatedRegister.SUB_AUTHENTICATED_REGISTER, "KEY_t_sub_authenticated_register_PRIMARY", SubAuthenticatedRegister.SUB_AUTHENTICATED_REGISTER.ID);
        public static final UniqueKey<ReportRedlistRecord> KEY_T_REPORT_REDLIST_PRIMARY = createUniqueKey(ReportRedlist.REPORT_REDLIST, "KEY_t_report_redlist_PRIMARY", ReportRedlist.REPORT_REDLIST.ID);

        public static final UniqueKey<IndividualShortVideoDetailsRecord> KEY_T_INDIVIDUAL_SHORT_VIDEO_DETAILS_PRIMARY = createUniqueKey(IndividualShortVideoDetails.INDIVIDUAL_SHORT_VIDEO_DETAILS, "KEY_t_individual_short_video_details_PRIMARY", IndividualShortVideoDetails.INDIVIDUAL_SHORT_VIDEO_DETAILS.ID);
        public static final UniqueKey<IndividualShortVideoSummaryRecord> KEY_T_INDIVIDUAL_SHORT_VIDEO_SUMMARY_PRIMARY = createUniqueKey(IndividualShortVideoSummary.INDIVIDUAL_SHORT_VIDEO_SUMMARY, "KEY_t_individual_short_video_summary_PRIMARY", IndividualShortVideoSummary.INDIVIDUAL_SHORT_VIDEO_SUMMARY.ID);
        public static final UniqueKey<PersonalShortVideoLearningDetailsRecord> KEY_T_PERSONAL_SHORT_VIDEO_LEARNING_DETAILS_PRIMARY = createUniqueKey(PersonalShortVideoLearningDetails.PERSONAL_SHORT_VIDEO_LEARNING_DETAILS, "KEY_t_personal_short_video_learning_details_PRIMARY", PersonalShortVideoLearningDetails.PERSONAL_SHORT_VIDEO_LEARNING_DETAILS.ID);
        public static final UniqueKey<ShortVideoCreateDetailsRecord> KEY_T_SHORT_VIDEO_CREATE_DETAILS_PRIMARY = createUniqueKey(ShortVideoCreateDetails.SHORT_VIDEO_CREATE_DETAILS, "KEY_t_short_video_create_details_PRIMARY", ShortVideoCreateDetails.SHORT_VIDEO_CREATE_DETAILS.ID);
        public static final UniqueKey<ShortVideoReportRecord> KEY_T_SHORT_VIDEO_REPORT_PRIMARY = createUniqueKey(ShortVideoReport.SHORT_VIDEO_REPORT, "KEY_t_short_video_report_PRIMARY", ShortVideoReport.SHORT_VIDEO_REPORT.ID);
        public static final UniqueKey<GenseeSignInRecord> KEY_T_GENSEE_SIGN_IN_PRIMARY = createUniqueKey(GenseeSignIn.GENSEE_SIGN_IN, "KEY_t_gensee_sign_in_PRIMARY", GenseeSignIn.GENSEE_SIGN_IN.ID);
        public static final UniqueKey<MiguUserAccessRecord> KEY_T_MIGU_USER_ACCESS_PRIMARY = createUniqueKey(MiguUserAccess.MIGU_USER_ACCESS, "KEY_t_migu_user_access_PRIMARY", MiguUserAccess.MIGU_USER_ACCESS.ID);
        public static final UniqueKey<MiguAttachmentRecord> KEY_T_MIGU_ATTACHMENT_PRIMARY = createUniqueKey(MiguAttachment.MIGU_ATTACHMENT, "KEY_t_migu_attachment_PRIMARY", MiguAttachment.MIGU_ATTACHMENT.ID);
        public static final UniqueKey<CourseMarkRecord> KEY_T_COURSE_MARK_PRIMARY = createUniqueKey(CourseMark.COURSE_MARK, "KEY_t_course_mark_PRIMARY", CourseMark.COURSE_MARK.ID);
        public static final UniqueKey<NotePraiseRecord> KEY_T_NOTE_PRAISE_PRIMARY = createUniqueKey(NotePraise.NOTE_PRAISE, "KEY_t_note_praise_PRIMARY", NotePraise.NOTE_PRAISE.ID);
        public static final UniqueKey<LiveVirtualSpaceRecord> KEY_T_LIVE_VIRTUAL_SPACE_PRIMARY = createUniqueKey(LiveVirtualSpace.LIVE_VIRTUAL_SPACE, "KEY_t_live_virtual_space_PRIMARY", LiveVirtualSpace.LIVE_VIRTUAL_SPACE.ID);

        public static final UniqueKey<GenseeShareRecord> KEY_T_GENSEE_SHARE_PRIMARY = createUniqueKey(GenseeShare.GENSEE_SHARE, "KEY_t_gensee_share_PRIMARY", GenseeShare.GENSEE_SHARE.ID);
        public static final UniqueKey<KnowledgeRedShipAuditRecord> KEY_T_KNOWLEDGE_RED_SHIP_AUDIT_PRIMARY = createUniqueKey(KnowledgeRedShipAudit.KNOWLEDGE_RED_SHIP_AUDIT, "KEY_t_knowledge_red_ship_audit_PRIMARY", KnowledgeRedShipAudit.KNOWLEDGE_RED_SHIP_AUDIT.ID);

        public static final UniqueKey<ShortVideoLogDayRecord> KEY_T_SHORT_VIDEO_LOG_DAY_PRIMARY = createUniqueKey(ShortVideoLogDay.SHORT_VIDEO_LOG_DAY, "KEY_t_short_video_log_day_PRIMARY", ShortVideoLogDay.SHORT_VIDEO_LOG_DAY.ID);
        public static final UniqueKey<AnnualBill_2023Record> KEY_T_ANNUAL_BILL_2023_PRIMARY = createUniqueKey(AnnualBill_2023.ANNUAL_BILL_2023, "KEY_t_annual_bill_2023_PRIMARY", AnnualBill_2023.ANNUAL_BILL_2023.ID);
        public static final UniqueKey<CourseSectionStudyProgressFfclc_2024Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_2024_PRIMARY = createUniqueKey(CourseSectionStudyProgressFfclc_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2024, "KEY_t_course_section_study_progress_ffclc_2024_PRIMARY", CourseSectionStudyProgressFfclc_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2024.ID);
        public static final UniqueKey<CourseSectionStudyProgressFfclc_2024Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLC_2024_UNIQUE_T_CSSP_FFCLC_2024_SECTION_MEMBER = createUniqueKey(CourseSectionStudyProgressFfclc_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2024, "KEY_t_course_section_study_progress_ffclc_2024_unique_t_cssp_ffclc_2024_section_member", CourseSectionStudyProgressFfclc_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2024.SECTION_ID, CourseSectionStudyProgressFfclc_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLC_2024.MEMBER_ID);
        public static final UniqueKey<CourseSectionStudyProgressFfcls_2024Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024_PRIMARY = createUniqueKey(CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024, "KEY_t_course_section_study_progress_ffcls_2024_PRIMARY", CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.ID);
        public static final UniqueKey<CourseSectionStudyProgressFfcls_2024Record> KEY_T_COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024_UNIQUE_T_CSSP_FFCLS_2024_SECTION_MEMBER = createUniqueKey(CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024, "KEY_t_course_section_study_progress_ffcls_2024_unique_t_cssp_ffcls_2024_section_member", CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.SECTION_ID, CourseSectionStudyProgressFfcls_2024.COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024.MEMBER_ID);
        public static final UniqueKey<DjLogRecord> KEY_T_DJ_LOG_PRIMARY = createUniqueKey(DjLog.DJ_LOG, "KEY_t_dj_log_PRIMARY", DjLog.DJ_LOG.ID);
        public static final UniqueKey<SignInRecord> KEY_T_SIGN_IN_PRIMARY = createUniqueKey(SignIn.SIGN_IN, "KEY_t_sign_in_PRIMARY", SignIn.SIGN_IN.ID);
        public static final UniqueKey<BroadcastCountRecord> KEY_T_BROADCAST_COUNT_PRIMARY = createUniqueKey(BroadcastCount.BROADCAST_COUNT, "KEY_t_broadcast_count_PRIMARY", BroadcastCount.BROADCAST_COUNT.ID);
        public static final UniqueKey<MemberCourseHoursRecord> KEY_T_MEMBER_COURSE_HOURS_PRIMARY = createUniqueKey(MemberCourseHours.MEMBER_COURSE_HOURS, "KEY_t_member_course_hours_PRIMARY", MemberCourseHours.MEMBER_COURSE_HOURS.ID);

        public static final UniqueKey<PartyBannerRecord> KEY_T_PARTY_BANNER_PRIMARY = createUniqueKey(PartyBanner.PARTY_BANNER, "KEY_t_party_banner_PRIMARY", PartyBanner.PARTY_BANNER.ID);
        public static final UniqueKey<PartyDemonstrationTrainingConfigRecord> KEY_T_PARTY_DEMONSTRATION_TRAINING_CONFIG_PRIMARY = createUniqueKey(PartyDemonstrationTrainingConfig.PARTY_DEMONSTRATION_TRAINING_CONFIG, "KEY_t_party_demonstration_training_config_PRIMARY", PartyDemonstrationTrainingConfig.PARTY_DEMONSTRATION_TRAINING_CONFIG.ID);
        public static final UniqueKey<PartyFocusConfigRecord> KEY_T_PARTY_FOCUS_CONFIG_PRIMARY = createUniqueKey(PartyFocusConfig.PARTY_FOCUS_CONFIG, "KEY_t_party_focus_config_PRIMARY", PartyFocusConfig.PARTY_FOCUS_CONFIG.ID);
        public static final UniqueKey<PartyDynamicNewsConfigRecord> KEY_T_PARTY_DYNAMIC_NEWS_CONFIG_PRIMARY = createUniqueKey(PartyDynamicNewsConfig.PARTY_DYNAMIC_NEWS_CONFIG, "KEY_t_party_dynamic_news_config_PRIMARY", PartyDynamicNewsConfig.PARTY_DYNAMIC_NEWS_CONFIG.ID);
        public static final UniqueKey<AiQuestionRecord> KEY_T_AI_QUESTION_PRIMARY = createUniqueKey(AiQuestion.AI_QUESTION, "KEY_t_ai_question_PRIMARY", AiQuestion.AI_QUESTION.ID);
        public static final UniqueKey<AiAnswerRecord> KEY_T_AI_ANSWER_PRIMARY = createUniqueKey(AiAnswer.AI_ANSWER,"KEY_t_ai_answer_PRIMARY",AiAnswer.AI_ANSWER.ID);
        public static final UniqueKey<AiFeedbackRecord> KEY_T_AI_FEEDBACK_PRIMARY = createUniqueKey(AiFeedback.AI_FEEDBACK, "KEY_t_ai_feedback_PRIMARY", AiFeedback.AI_FEEDBACK.ID);
        public static final UniqueKey<AiMentorRecord> KEY_T_AI_MENTOR_PRIMARY = createUniqueKey(AiMentor.AI_MENTOR, "KEY_t_ai_mentor_PRIMARY", AiMentor.AI_MENTOR.ID);
        public static final UniqueKey<AiPresetExampleRecord> KEY_T_AI_PRESET_EXAMPLE_PRIMARY = createUniqueKey(AiPresetExample.AI_PRESET_EXAMPLE, "KEY_t_ai_preset_example_PRIMARY", AiPresetExample.AI_PRESET_EXAMPLE.ID);

        public static final UniqueKey<GbCourseAuditRecord> KEY_T_GB_COURSE_AUDIT_PRIMARY = createUniqueKey(GbCourseAudit.GB_COURSE_AUDIT, "KEY_t_gb_course_audit_PRIMARY", GbCourseAudit.GB_COURSE_AUDIT.ID);
        public static final UniqueKey<GbCourseClassificationRecord> KEY_T_GB_COURSE_CLASSIFICATION_PRIMARY = createUniqueKey(GbCourseClassification.GB_COURSE_CLASSIFICATION, "KEY_t_gb_course_classification_PRIMARY", GbCourseClassification.GB_COURSE_CLASSIFICATION.ID);
        public static final UniqueKey<GbCourseConfigurationRecord> KEY_T_GB_COURSE_CONFIGURATION_PRIMARY = createUniqueKey(GbCourseConfiguration.GB_COURSE_CONFIGURATION, "KEY_t_gb_course_configuration_PRIMARY", GbCourseConfiguration.GB_COURSE_CONFIGURATION.ID);
        public static final UniqueKey<GbCourseLibraryRecord> KEY_T_GB_COURSE_LIBRARY_PRIMARY = createUniqueKey(GbCourseLibrary.GB_COURSE_LIBRARY, "KEY_t_gb_course_library_PRIMARY", GbCourseLibrary.GB_COURSE_LIBRARY.ID);
        public static final UniqueKey<GbCourseMiddleRecord> KEY_T_GB_COURSE_MIDDLE_PRIMARY = createUniqueKey(GbCourseMiddle.GB_COURSE_MIDDLE, "KEY_t_gb_course_middle_PRIMARY", GbCourseMiddle.GB_COURSE_MIDDLE.ID);
        public static final UniqueKey<GbCourseRecordRecord> KEY_T_GB_COURSE_RECORD_PRIMARY = createUniqueKey(GbCourseRecord.GB_COURSE_RECORD, "KEY_t_gb_course_record_PRIMARY", GbCourseRecord.GB_COURSE_RECORD.ID);
        public static final UniqueKey<GbLecturerLibraryRecord> KEY_T_GB_LECTURER_LIBRARY_PRIMARY = createUniqueKey(GbLecturerLibrary.GB_LECTURER_LIBRARY, "KEY_t_gb_lecturer_library_PRIMARY", GbLecturerLibrary.GB_LECTURER_LIBRARY.ID);
        public static final UniqueKey<GbMemberRecord> KEY_T_GB_MEMBER_PRIMARY = createUniqueKey(GbMember.GB_MEMBER, "KEY_t_gb_member_PRIMARY", GbMember.GB_MEMBER.ID);
        public static final UniqueKey<SubjectPlanRelatedRecord> KEY_T_SUBJECT_PLAN_RELATED_PRIMARY = createUniqueKey(SubjectPlanRelated.SUBJECT_PLAN_RELATED, "KEY_t_subject_plan_related_PRIMARY", SubjectPlanRelated.SUBJECT_PLAN_RELATED.ID);
        public static final UniqueKey<CoursewareNoteRecord> KEY_T_COURSEWARE_NOTE_PRIMARY = createUniqueKey(CoursewareNote.COURSEWARE_NOTE, "KEY_t_courseware_note_PRIMARY", CoursewareNote.COURSEWARE_NOTE.ID);
        public static final UniqueKey<CoursewareNoteAuditRecord> KEY_T_COURSEWARE_NOTE_AUDIT_PRIMARY = createUniqueKey(CoursewareNoteAudit.COURSEWARE_NOTE_AUDIT, "KEY_t_courseware_note_audit_PRIMARY", CoursewareNoteAudit.COURSEWARE_NOTE_AUDIT.ID);
        public static final UniqueKey<CoursewareNoteVersionRecord> KEY_T_COURSEWARE_NOTE_VERSION_PRIMARY = createUniqueKey(CoursewareNoteVersion.COURSEWARE_NOTE_VERSION, "KEY_t_courseware_note_version_PRIMARY", CoursewareNoteVersion.COURSEWARE_NOTE_VERSION.ID);
        public static final UniqueKey<CourseMainNoteRecord> KEY_T_COURSE_MAIN_NOTE_PRIMARY = createUniqueKey(CourseMainNote.COURSE_MAIN_NOTE, "KEY_t_course_main_note_PRIMARY", CourseMainNote.COURSE_MAIN_NOTE.ID);
        public static final UniqueKey<CourseMainNoteVersionRecord> KEY_T_COURSE_MAIN_NOTE_VERSION_PRIMARY = createUniqueKey(CourseMainNoteVersion.COURSE_MAIN_NOTE_VERSION, "KEY_t_course_main_note_version_PRIMARY", CourseMainNoteVersion.COURSE_MAIN_NOTE_VERSION.ID);
        public static final UniqueKey<CourseMainNoteAuditRecord> KEY_T_COURSE_MAIN_NOTE_AUDIT_PRIMARY = createUniqueKey(CourseMainNoteAudit.COURSE_MAIN_NOTE_AUDIT, "KEY_t_course_main_note_audit_PRIMARY", CourseMainNoteAudit.COURSE_MAIN_NOTE_AUDIT.ID);

        public static final UniqueKey<ShortVideoOperationRecord> KEY_T_SHORT_VIDEO_OPERATION_PRIMARY = createUniqueKey(ShortVideoOperation.SHORT_VIDEO_OPERATION, "KEY_t_short_video_operation_PRIMARY", ShortVideoOperation.SHORT_VIDEO_OPERATION.ID);
        public static final UniqueKey<ShortVideoOperationGroupRecord> KEY_T_SHORT_VIDEO_OPERATION_GROUP_PRIMARY = createUniqueKey(ShortVideoOperationGroup.SHORT_VIDEO_OPERATION_GROUP, "KEY_t_short_video_operation_group_PRIMARY", ShortVideoOperationGroup.SHORT_VIDEO_OPERATION_GROUP.ID);
        public static final UniqueKey<ShortVideoOperationIntegralRecord> KEY_T_SHORT_VIDEO_OPERATION_INTEGRAL_PRIMARY = createUniqueKey(ShortVideoOperationIntegral.SHORT_VIDEO_OPERATION_INTEGRAL, "KEY_t_short_video_operation_integral_PRIMARY", ShortVideoOperationIntegral.SHORT_VIDEO_OPERATION_INTEGRAL.ID);
        public static final UniqueKey<ShortVideoOperationMemberRecord> KEY_T_SHORT_VIDEO_OPERATION_MEMBER_PRIMARY = createUniqueKey(ShortVideoOperationMember.SHORT_VIDEO_OPERATION_MEMBER, "KEY_t_short_video_operation_member_PRIMARY", ShortVideoOperationMember.SHORT_VIDEO_OPERATION_MEMBER.ID);
        public static final UniqueKey<ShortVideoOperationPickQuestionRecord> KEY_T_SHORT_VIDEO_OPERATION_PICK_QUESTION_PRIMARY = createUniqueKey(ShortVideoOperationPickQuestion.SHORT_VIDEO_OPERATION_PICK_QUESTION, "KEY_t_short_video_operation_pick_question_PRIMARY", ShortVideoOperationPickQuestion.SHORT_VIDEO_OPERATION_PICK_QUESTION.ID);
        public static final UniqueKey<ShortVideoOperationQuestionRecord> KEY_T_SHORT_VIDEO_OPERATION_QUESTION_PRIMARY = createUniqueKey(ShortVideoOperationQuestion.SHORT_VIDEO_OPERATION_QUESTION, "KEY_t_short_video_operation_question_PRIMARY", ShortVideoOperationQuestion.SHORT_VIDEO_OPERATION_QUESTION.ID);
        public static final UniqueKey<IshowSdkRequestRecord> KEY_T_ISHOW_SDK_REQUEST_PRIMARY = createUniqueKey(IshowSdkRequest.ISHOW_SDK_REQUEST, "KEY_t_ishow_sdk_request_PRIMARY", IshowSdkRequest.ISHOW_SDK_REQUEST.ID);
        public static final UniqueKey<IshowStudyRecordRecord> KEY_T_ISHOW_STUDY_RECORD_PRIMARY = createUniqueKey(IshowStudyRecord.ISHOW_STUDY_RECORD, "KEY_t_ishow_study_record_PRIMARY", IshowStudyRecord.ISHOW_STUDY_RECORD.ID);

        public static final UniqueKey<ApplicationConfigRecord> KEY_T_APPLICATION_CONFIG_PRIMARY = createUniqueKey(ApplicationConfig.APPLICATION_CONFIG, "KEY_t_application_config_PRIMARY", ApplicationConfig.APPLICATION_CONFIG.ID);
        public static final UniqueKey<AbilityRecord> KEY_T_ABILITY_PRIMARY = createUniqueKey(Ability.ABILITY, "KEY_t_ability_PRIMARY", Ability.ABILITY.ID);
        public static final UniqueKey<AbilityBusinessRecord> KEY_T_ABILITY_BUSINESS_PRIMARY = createUniqueKey(AbilityBusiness.ABILITY_BUSINESS, "KEY_t_ability_business_PRIMARY", AbilityBusiness.ABILITY_BUSINESS.ID);
        public static final UniqueKey<AbilityBusinessRecord> KEY_T_ABILITY_BUSINESS_UNIQ_ABILITY_ID_BUSINESS_ID = createUniqueKey(AbilityBusiness.ABILITY_BUSINESS, "KEY_t_ability_business_uniq_ability_id_business_id", AbilityBusiness.ABILITY_BUSINESS.ABILITY_ID, AbilityBusiness.ABILITY_BUSINESS.BUSINESS_ID);
        public static final UniqueKey<CourseAbilityRecord> KEY_T_COURSE_ABILITY_PRIMARY = createUniqueKey(CourseAbility.COURSE_ABILITY, "KEY_t_course_ability_PRIMARY", CourseAbility.COURSE_ABILITY.ID);
        public static final UniqueKey<StudyReportAnalysisManagersRecord> KEY_T_STUDY_REPORT_ANALYSIS_MANAGERS_PRIMARY = createUniqueKey(StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS, "KEY_t_study_report_analysis_managers_PRIMARY", StudyReportAnalysisManagers.STUDY_REPORT_ANALYSIS_MANAGERS.ID);

        public static final UniqueKey<SubjectRankYearRecord> KEY_T_SUBJECT_RANK_YEAR_PRIMARY =createUniqueKey(SubjectRankYear.SUBJECT_RANK_YEAR, "KEY_t_subject_rank_year_PRIMARY", SubjectRankYear.SUBJECT_RANK_YEAR.ID);
        public static final UniqueKey<CourseRenRecord> KEY_T_COURSE_REN_PRIMARY = createUniqueKey(CourseRen.COURSE_REN, "KEY_t_course_ren_PRIMARY", CourseRen.COURSE_REN.ID);
        public static final UniqueKey<CourseRenFeedbackRecord> KEY_T_COURSE_REN_FEEDBACK_PRIMARY = createUniqueKey(CourseRenFeedback.COURSE_REN_FEEDBACK, "KEY_t_course_ren_feedback_PRIMARY", CourseRenFeedback.COURSE_REN_FEEDBACK.ID);
        public static final UniqueKey<AiSynchronousRecord> KEY_T_AI_SYNCHRONOUS_PRIMARY = createUniqueKey(AiSynchronous.AI_SYNCHRONOUS, "KEY_t_ai_synchronous_PRIMARY", AiSynchronous.AI_SYNCHRONOUS.ID);
        public static final UniqueKey<CourseOnlineLogRecord> KEY_T_COURSE_ONLINE_LOG_PRIMARY = createUniqueKey(CourseOnlineLog.COURSE_ONLINE_LOG, "KEY_t_course_online_log_PRIMARY", CourseOnlineLog.COURSE_ONLINE_LOG.ID);
        public static final UniqueKey<DigitalMentorCallbackRecord> KEY_T_DIGITAL_MENTOR_CALLBACK_PRIMARY = createUniqueKey(DigitalMentorCallback.DIGITAL_MENTOR_CALLBACK, "KEY_t_digital_mentor_callback_PRIMARY", DigitalMentorCallback.DIGITAL_MENTOR_CALLBACK.ID);

        public static final UniqueKey<PartySchoolNewsRecord> KEY_T_PARTY_SCHOOL_NEWS_PRIMARY = createUniqueKey(PartySchoolNews.PARTY_SCHOOL_NEWS, "KEY_t_party_school_news_PRIMARY", PartySchoolNews.PARTY_SCHOOL_NEWS.ID);
        public static final UniqueKey<CourseFeedbackRecord> KEY_T_COURSE_FEEDBACK_PRIMARY = createUniqueKey(CourseFeedback.COURSE_FEEDBACK, "KEY_t_course_feedback_PRIMARY", CourseFeedback.COURSE_FEEDBACK.ID);
        public static final UniqueKey<CourseKnowledgeRecord> KEY_T_COURSE_KNOWLEDGE_PRIMARY = createUniqueKey(CourseKnowledge.COURSE_KNOWLEDGE, "KEY_t_course_knowledge_PRIMARY", CourseKnowledge.COURSE_KNOWLEDGE.ID);
        public static final UniqueKey<CourseQuestionRecommendRecord> KEY_T_COURSE_QUESTION_RECOMMEND_PRIMARY = createUniqueKey(CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND, "KEY_t_course_question_recommend_PRIMARY", CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.ID);
        public static final UniqueKey<ChatTimeRecordRecord> KEY_T_CHAT_TIME_RECORD_PRIMARY = createUniqueKey(ChatTimeRecord.CHAT_TIME_RECORD, "KEY_t_chat_time_record_PRIMARY", ChatTimeRecord.CHAT_TIME_RECORD.ID);

        public static final UniqueKey<SubAuthenticatedTmpRecord> KEY_T_SUB_AUTHENTICATED_TMP_PRIMARY = createUniqueKey(SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP, "KEY_t_sub_authenticated_tmp_PRIMARY", SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.ID);
    }
}
