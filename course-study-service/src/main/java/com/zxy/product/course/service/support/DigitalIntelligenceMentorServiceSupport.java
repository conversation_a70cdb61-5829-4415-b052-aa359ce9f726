package com.zxy.product.course.service.support;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.DigitalIntelligenceMentorService;
import com.zxy.product.course.entity.DigitalIntelligenceMentor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR;

/**
 * 数智导师对话记录服务实现类
 */
@Service
public class DigitalIntelligenceMentorServiceSupport implements DigitalIntelligenceMentorService {

    private CommonDao<DigitalIntelligenceMentor> digitalIntelligenceMentorDao;

    @Autowired
    public void setDigitalIntelligenceMentorDao(CommonDao<DigitalIntelligenceMentor> digitalIntelligenceMentorDao) {
        this.digitalIntelligenceMentorDao = digitalIntelligenceMentorDao;
    }

    @Override
    public List<DigitalIntelligenceMentor> findLatestConversationsByMemberId(String memberId, Integer limit) {
        return digitalIntelligenceMentorDao.execute(context ->
            context.select()
                .from(DIGITAL_INTELLIGENCE_MENTOR)
                .where(DIGITAL_INTELLIGENCE_MENTOR.MEMBER_ID.eq(memberId))
                .orderBy(DIGITAL_INTELLIGENCE_MENTOR.ORDER.desc())
                .limit(limit)
                .fetch(r -> r.into(DigitalIntelligenceMentor.class))
        );
    }

    @Override
    public DigitalIntelligenceMentor insert(DigitalIntelligenceMentor digitalIntelligenceMentor) {
        digitalIntelligenceMentorDao.insert(digitalIntelligenceMentor);
        return digitalIntelligenceMentor;
    }

    @Override
    public DigitalIntelligenceMentor get(String id) {
        return digitalIntelligenceMentorDao.get(id);
    }

    @Override
    public void deleteByMemberId(String memberId) {
        digitalIntelligenceMentorDao.delete(DIGITAL_INTELLIGENCE_MENTOR.MEMBER_ID.eq(memberId));
    }

    @Override
    public void updateBotResponse(String id, String botResponse) {
        digitalIntelligenceMentorDao.execute(context ->
            context.update(DIGITAL_INTELLIGENCE_MENTOR)
                .set(DIGITAL_INTELLIGENCE_MENTOR.BOT_RESPONSE, botResponse)
                .where(DIGITAL_INTELLIGENCE_MENTOR.ID.eq(id))
                .execute()
        );
    }
}
